/* Main CSS for Jackpot Game */

/* Game layout */
.sl-game {
    font-family: Arial, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.sl-game-logo {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 150px;
    height: auto;
    z-index: 10;
}

.sl-game-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    position: relative;
    padding: 20px;
}

.sl-game-inner {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 800px;
    z-index: 2;
}

.sl-game-girlImage {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 80vh;
    max-height: 800px;
    z-index: 1;
}

/* Text cloud */
.textCloud {
    position: relative;
    margin-bottom: 30px;
    width: 300px;
}

.textCloud img {
    width: 100%;
}

.textCloudInner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 24px;
    color: #333;
}

.textCloudInner__first {
    font-size: 28px;
    color: #000;
}

/* Buttons */
.sl-game-button {
    position: relative;
    background: linear-gradient(to bottom, #4a90e2, #3672b5);
    border: none;
    border-radius: 50px;
    color: white;
    padding: 15px 30px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    overflow: hidden;
    min-width: 200px;
}

.sl-game-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.sl-game-button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.sl-game-button img {
    width: 24px;
    height: 24px;
}

.sl-game-button .splash {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0);
    transition: transform 0.5s, opacity 0.5s;
}

.sl-game-button:active .splash {
    opacity: 1;
    transform: scale(2);
}

/* Button states */
.sl-game-button.disabled {
    background: linear-gradient(to bottom, #a0a0a0, #808080);
    cursor: not-allowed;
}

.sl-game-button.connecting {
    background: linear-gradient(to bottom, #3396FF, #2376CF);
    animation: pulse 1.5s infinite;
}

.sl-game-button.switching {
    background: linear-gradient(to bottom, #FF9800, #F57C00);
    animation: pulse 1.5s infinite;
}

.sl-game-button.transferring {
    background: linear-gradient(to bottom, #4CAF50, #388E3C);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(51, 150, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(51, 150, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(51, 150, 255, 0);
    }
}

/* Game field */
.gameField {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    padding: 20px;
    margin-top: 20px;
    width: 100%;
    max-width: 700px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.gameField.hidden {
    display: none;
}

.gameField__head {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
}

.infoText {
    font-size: 24px;
    color: #ffffff;
    text-align: center;
    margin-top: 10px;
}

.gameFieldImg {
    width: 100%;
    max-width: 600px;
    height: 400px;
    background-color: #2a2a2a;
    border-radius: 10px;
    margin-bottom: 20px;
}

/* Stars animation */
.sl-game-stars {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 10px;
}

.sl-game-stars div {
    width: 30px;
    height: 30px;
    background-image: url('/images/star.png');
    background-size: contain;
    background-repeat: no-repeat;
}

.sl-game-stars div.animated {
    animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.7;
    }
    100% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 100;
    justify-content: center;
    align-items: center;
}

.modal_content {
    background-color: #2a2a2a;
    border-radius: 20px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.shadow {
    position: relative;
}

.shadow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    box-shadow: 0 0 50px 10px rgba(255, 215, 0, 0.5);
    z-index: -1;
}

/* Popup labels */
.sl-game-popup-labels {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
    width: 100%;
}

.sl-game-popupLabel {
    margin-bottom: 15px;
    text-align: center;
}

.popupTitle {
    font-size: 36px;
    font-weight: bold;
    color: #ffcc00;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

.popupInfo {
    font-size: 28px;
    color: #ffffff;
}

.popupWinText {
    font-size: 22px;
    color: #ffffff;
}

/* Fireworks canvas */
.firework {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 50;
}

/* Footer */
.sl-game-footer {
    margin-top: 30px;
    text-align: center;
    font-size: 14px;
    color: #888;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sl-game-girlImage {
        height: 60vh;
        opacity: 0.5;
    }

    .textCloud {
        width: 250px;
    }

    .textCloudInner {
        font-size: 20px;
    }

    .textCloudInner__first {
        font-size: 24px;
    }

    .sl-game-button {
        font-size: 20px;
        padding: 12px 25px;
    }

    .gameFieldImg {
        height: 300px;
    }

    .popupTitle {
        font-size: 30px;
    }

    .popupInfo {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .sl-game-logo {
        width: 100px;
    }

    .sl-game-girlImage {
        height: 50vh;
        opacity: 0.3;
    }

    .textCloud {
        width: 200px;
    }

    .textCloudInner {
        font-size: 18px;
    }

    .textCloudInner__first {
        font-size: 20px;
    }

    .sl-game-button {
        font-size: 18px;
        padding: 10px 20px;
        min-width: 180px;
    }

    .gameFieldImg {
        height: 250px;
    }

    .popupTitle {
        font-size: 26px;
    }

    .popupInfo {
        font-size: 20px;
    }

    .popupWinText {
        font-size: 18px;
    }
}
