{"traceEvents": [{"pid": 635, "tid": 0, "ts": 0, "ph": "M", "cat": "__metadata", "name": "num_cpus", "args": {"number": 2}}, {"pid": 635, "tid": 635, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_sort_index", "args": {"sort_index": -6}}, {"pid": 635, "tid": 635, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_name", "args": {"name": "HeadlessBrowser"}}, {"pid": 635, "tid": 635, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_uptime_seconds", "args": {"uptime": 4}}, {"pid": 653, "tid": 0, "ts": 0, "ph": "M", "cat": "__metadata", "name": "num_cpus", "args": {"number": 2}}, {"pid": 653, "tid": 653, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_sort_index", "args": {"sort_index": -1}}, {"pid": 653, "tid": 653, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_name", "args": {"name": "GPU Process"}}, {"pid": 653, "tid": 653, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_uptime_seconds", "args": {"uptime": 4}}, {"pid": 660, "tid": 660, "ts": 39184281286, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 1, "totalObjects": 3, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 39955}, {"pid": 660, "tid": 660, "ts": 39184281639, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 1, "totalObjects": 3, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 40158}, {"pid": 660, "tid": 660, "ts": 39184281838, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 2}}, "tts": 40226}, {"pid": 660, "tid": 660, "ts": 39184281857, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 2}}, "tts": 40244}, {"pid": 660, "tid": 660, "ts": 39184281874, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 132, "tdur": 132, "tts": 40261}, {"pid": 660, "tid": 660, "ts": 39184282078, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 2, "layerId": 11}}, "dur": 47, "tdur": 47, "tts": 40465}, {"pid": 660, "tid": 660, "ts": 39184284973, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "resize"}}, "dur": 5, "tdur": 5, "tts": 40839}, {"pid": 660, "tid": 660, "ts": 39184284998, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 161, "tdur": 161, "tts": 40864}, {"pid": 660, "tid": 660, "ts": 39184288700, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "beforeunload"}}, "dur": 5, "tdur": 5, "tts": 41514}, {"pid": 660, "tid": 660, "ts": 39184301957, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 58, "tdur": 30, "tts": 41801}, {"pid": 660, "tid": 660, "ts": 39184302017, "ph": "B", "cat": "blink,devtools.timeline", "name": "HitTest", "args": {}, "tts": 41834}, {"pid": 660, "tid": 660, "ts": 39184302084, "ph": "E", "cat": "blink,devtools.timeline", "name": "HitTest", "args": {"endData": {"x": 0, "y": 0, "rectilinear": true, "move": true, "nodeId": 3, "nodeName": "HTML"}}, "tts": 41901}, {"pid": 660, "tid": 660, "ts": 39184388416, "ph": "I", "cat": "devtools.timeline", "name": "FrameStartedLoading", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 42382, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184388705, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "1"}}, "tts": 42670}, {"pid": 660, "tid": 660, "ts": 39184388719, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "F7E54A75A9D029464DC73FECC1CA8009", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/", "requestMethod": "GET", "priority": "VeryHigh"}}, "tts": 42684, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184388742, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 42707}, {"pid": 660, "tid": 660, "ts": 39184388958, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "F7E54A75A9D029464DC73FECC1CA8009", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "text/html", "encodedDataLength": 446.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.289384, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": 15.736, "dnsEnd": 25.246, "connectStart": 25.246, "connectEnd": 40.468, "sslStart": 26.482, "sslEnd": 40.418, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 40.679, "sendEnd": 40.835, "receiveHeadersEnd": 96.59, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 42923, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184389404, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "F7E54A75A9D029464DC73FECC1CA8009", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 3743}}, "tts": 43348, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184390000, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "pagehide"}}, "dur": 5, "tdur": 5, "tts": 43500}, {"pid": 660, "tid": 660, "ts": 39184390020, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "visibilitychange"}}, "dur": 3, "tdur": 3, "tts": 43520}, {"pid": 660, "tid": 660, "ts": 39184390026, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "webkitvisibilitychange"}}, "dur": 2, "tdur": 1, "tts": 43526}, {"pid": 660, "tid": 660, "ts": 39184390030, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "unload"}}, "dur": 2, "tdur": 1, "tts": 43530}, {"pid": 660, "tid": 660, "ts": 39184391988, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "readystatechange"}}, "dur": 6, "tdur": 5, "tts": 45413}, {"pid": 660, "tid": 660, "ts": 39184392672, "ph": "X", "cat": "devtools.timeline", "name": "CommitLoad", "args": {"data": {"isMainFrame": true, "page": "BFA6B40D1E4405E5925EEA32B23C3F03", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/", "name": ""}}, "dur": 139, "tdur": 140, "tts": 45883}, {"pid": 660, "tid": 660, "ts": 39184393234, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "F7E54A75A9D029464DC73FECC1CA8009", "didFail": false, "encodedDataLength": 1568.0, "decodedBodyLength": 3743.0, "finishTime": 39184.387924}}, "tts": 46403, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184395798, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 47380}, {"pid": 660, "tid": 660, "ts": 39184395928, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "2"}}, "tts": 47510}, {"pid": 660, "tid": 660, "ts": 39184395946, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.2", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/css/app.min.css?version=1.3", "requestMethod": "GET", "priority": "VeryHigh"}}, "tts": 47528, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184395983, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 47565}, {"pid": 660, "tid": 660, "ts": 39184396217, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "3"}}, "tts": 47710}, {"pid": 660, "tid": 660, "ts": 39184396226, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.3", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/eng/logo.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 47718, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396249, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 47742}, {"pid": 660, "tid": 660, "ts": 39184396502, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "4"}}, "tts": 47994}, {"pid": 660, "tid": 660, "ts": 39184396509, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.4", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/v1.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 48002, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396534, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 48027}, {"pid": 660, "tid": 660, "ts": 39184396644, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "5"}}, "tts": 48136}, {"pid": 660, "tid": 660, "ts": 39184396651, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.5", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/textCloud.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 48143, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396677, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 48170}, {"pid": 660, "tid": 660, "ts": 39184396723, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "6"}}, "tts": 48216}, {"pid": 660, "tid": 660, "ts": 39184396729, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.6", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/play_btn.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 48221, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396767, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 48259}, {"pid": 660, "tid": 660, "ts": 39184396813, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "7"}}, "tts": 48305}, {"pid": 660, "tid": 660, "ts": 39184396819, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.7", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/open_btn.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 48311, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396838, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 48331}, {"pid": 660, "tid": 660, "ts": 39184396878, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "8"}}, "tts": 48370}, {"pid": 660, "tid": 660, "ts": 39184396884, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.8", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/take_btn.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 48376, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396904, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 48397}, {"pid": 660, "tid": 660, "ts": 39184396948, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "9"}}, "tts": 48440}, {"pid": 660, "tid": 660, "ts": 39184396953, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.9", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "requestMethod": "GET", "priority": "Medium"}}, "tts": 48445, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184396975, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 48467}, {"pid": 660, "tid": 660, "ts": 39184398358, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": 116}}, "tts": 49851}, {"pid": 660, "tid": 660, "ts": 39184399629, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 117, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 51023}, {"pid": 660, "tid": 660, "ts": 39184400087, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": 116}}, "tts": 51315}, {"pid": 660, "tid": 660, "ts": 39184435730, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.2", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "text/css", "encodedDataLength": 365.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.396508, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.812, "sendEnd": 1.793, "receiveHeadersEnd": 38.641, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 51766, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184436013, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.2", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 65536}}, "tts": 51932, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184437897, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.2", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 37604}}, "tts": 53676, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184438217, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.2", "didFail": false, "encodedDataLength": 13446.0, "decodedBodyLength": 103140.0, "finishTime": 39184.436519}}, "tts": 53974, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184439278, "ph": "X", "cat": "blink,devtools.timeline", "name": "ParseAuthorStyleSheet", "args": {"data": {"styleSheetUrl": "https://cristaldiamondofcanada.com/css/app.min.css?version=1.3"}}, "dur": 3420, "tdur": 3322, "tts": 54289}, {"pid": 660, "tid": 660, "ts": 39184443914, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.3", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 321.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.396966, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 1.279, "sendEnd": 4.07, "receiveHeadersEnd": 44.939, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 58023, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184444217, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.3", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 56298}}, "tts": 58326, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184444555, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.3", "didFail": false, "encodedDataLength": 56691.0, "decodedBodyLength": 56298.0, "finishTime": 39184.442844}}, "tts": 58636, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184444943, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 6, "tdur": 6, "tts": 58977}, {"pid": 660, "tid": 660, "ts": 39184445047, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 59081}, {"pid": 660, "tid": 660, "ts": 39184451072, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "58"}}, "tts": 62787}, {"pid": 660, "tid": 660, "ts": 39184451084, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.58", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/background-desktop.jpg", "requestMethod": "GET", "priority": "Low"}}, "tts": 62797, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184451118, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 62831}, {"pid": 660, "tid": 660, "ts": 39184451417, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "59"}}, "tts": 63131}, {"pid": 660, "tid": 660, "ts": 39184451426, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.59", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/animated_side_right.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 63139, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184451452, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 63166}, {"pid": 660, "tid": 660, "ts": 39184451640, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "1", "state": "pending", "name": "", "nodeId": 4, "nodeName": "DIV class='sl-game-slots sl-game-slots__right'"}}, "tts": 63354, "id": "0xaf627bd6d164b183"}, {"pid": 660, "tid": 660, "ts": 39184452292, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "2", "state": "pending", "name": "", "nodeId": 5, "nodeName": "DIV class='sl-game-button__label'"}}, "tts": 63973, "id": "0xaf627bd6d16498eb"}, {"pid": 660, "tid": 660, "ts": 39184452404, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "3", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 64085, "id": "0xaf627bd6d1648dcb"}, {"pid": 660, "tid": 660, "ts": 39184452610, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "4", "state": "pending", "name": "", "nodeId": 7, "nodeName": "BUTTON id='playButton' class='sl-game-button sl-game__playButton'"}}, "tts": 64291, "id": "0xaf627bd6d164fed3"}, {"pid": 660, "tid": 660, "ts": 39184452662, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "5", "state": "pending", "name": "", "nodeId": 8, "nodeName": "SPAN class='sl-game-button__label'"}}, "tts": 64343, "id": "0xaf627bd6d164f6bb"}, {"pid": 660, "tid": 660, "ts": 39184452814, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "6", "state": "pending", "name": "", "nodeId": 9, "nodeName": "I class='splash'"}}, "tts": 64496, "id": "0xaf627bd6d164ede3"}, {"pid": 660, "tid": 660, "ts": 39184452997, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "60"}}, "tts": 64678}, {"pid": 660, "tid": 660, "ts": 39184453004, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.60", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/images/animated_side_left.png", "requestMethod": "GET", "priority": "Low"}}, "tts": 64685, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184453024, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 64705}, {"pid": 660, "tid": 660, "ts": 39184453201, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "7", "state": "pending", "name": "", "nodeId": 10, "nodeName": "DIV class='sl-game-slots sl-game-slots__left'"}}, "tts": 64882, "id": "0xaf627bd6d164d753"}, {"pid": 660, "tid": 660, "ts": 39184454890, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "52"}}, "tts": 65548}, {"pid": 660, "tid": 660, "ts": 39184454899, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.52", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/fonts/<PERSON><PERSON>-<PERSON>.woff", "requestMethod": "GET", "priority": "VeryHigh"}}, "tts": 65556, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184454923, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 65579}, {"pid": 660, "tid": 660, "ts": 39184455988, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "46"}}, "tts": 65776}, {"pid": 660, "tid": 660, "ts": 39184455997, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.46", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/fonts/<PERSON>roy-Medium.woff", "requestMethod": "GET", "priority": "VeryHigh"}}, "tts": 65784, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184456020, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 65807}, {"pid": 660, "tid": 660, "ts": 39184456215, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 38}, "tts": 66003}, {"pid": 660, "tid": 660, "ts": 39184456226, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 47, "totalObjects": 47, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 66014}, {"pid": 660, "tid": 660, "ts": 39184461312, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "42"}}, "tts": 67337}, {"pid": 660, "tid": 660, "ts": 39184461323, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.42", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/fonts/<PERSON><PERSON>-<PERSON>.woff", "requestMethod": "GET", "priority": "VeryHigh"}}, "tts": 67348, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184461352, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 67376}, {"pid": 660, "tid": 660, "ts": 39184468004, "ph": "X", "cat": "devtools.timeline", "name": "ResourceChangePriority", "args": {"data": {"requestId": "1000000660.60", "priority": "High"}}, "dur": 10, "tdur": 9, "tts": 70056}, {"pid": 660, "tid": 660, "ts": 39184468051, "ph": "X", "cat": "devtools.timeline", "name": "ResourceChangePriority", "args": {"data": {"requestId": "1000000660.7", "priority": "High"}}, "dur": 3, "tdur": 3, "tts": 70102}, {"pid": 660, "tid": 660, "ts": 39184468076, "ph": "X", "cat": "devtools.timeline", "name": "ResourceChangePriority", "args": {"data": {"requestId": "1000000660.5", "priority": "High"}}, "dur": 3, "tdur": 3, "tts": 70127}, {"pid": 660, "tid": 660, "ts": 39184468101, "ph": "X", "cat": "devtools.timeline", "name": "ResourceChangePriority", "args": {"data": {"requestId": "1000000660.59", "priority": "High"}}, "dur": 3, "tdur": 3, "tts": 70152}, {"pid": 660, "tid": 660, "ts": 39184468133, "ph": "X", "cat": "devtools.timeline", "name": "ResourceChangePriority", "args": {"data": {"requestId": "1000000660.58", "priority": "High"}}, "dur": 3, "tdur": 2, "tts": 70185}, {"pid": 660, "tid": 660, "ts": 39184468163, "ph": "X", "cat": "devtools.timeline", "name": "ResourceChangePriority", "args": {"data": {"requestId": "1000000660.6", "priority": "High"}}, "dur": 3, "tdur": 3, "tts": 70214}, {"pid": 660, "tid": 660, "ts": 39184468202, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 70253}, {"pid": 660, "tid": 660, "ts": 39184468230, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 454, "tdur": 454, "tts": 70281}, {"pid": 660, "tid": 660, "ts": 39184468698, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 240, "tdur": 231, "tts": 70749}, {"pid": 660, "tid": 660, "ts": 39184468979, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [960.0, 1020.0, 961.0, 1020.0, 961.0, 1021.0, 960.0, 1021.0], "nodeId": 7, "layerId": 13}}, "dur": 12, "tdur": 12, "tts": 71021}, {"pid": 660, "tid": 660, "ts": 39184469004, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [961.0, 1020.0, 961.0, 1020.0, 961.0, 1020.0, 961.0, 1020.0], "nodeId": 9, "layerId": 15}}, "dur": 48, "tdur": 48, "tts": 71046}, {"pid": 660, "tid": 660, "ts": 39184469072, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [730.0, 544.0, 757.0, 544.0, 757.0, 570.0, 730.0, 570.0], "nodeId": 12, "layerId": 18}}, "dur": 5, "tdur": 6, "tts": 71113}, {"pid": 660, "tid": 660, "ts": 39184470338, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.4", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 313.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.397225, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 1.057, "sendEnd": 3.813, "receiveHeadersEnd": 46.562, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 71958, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184470449, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.4", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 65536}}, "tts": 72069, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184470858, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 6, "tdur": 5, "tts": 72478}, {"pid": 660, "tid": 660, "ts": 39184471013, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.9", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "application/javascript", "encodedDataLength": 328.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.398373, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 2.691, "sendEnd": 3.004, "receiveHeadersEnd": 47.789, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 72632, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184471103, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.9", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 42559}}, "tts": 72722, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184471703, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.9", "didFail": false, "encodedDataLength": 13186.0, "decodedBodyLength": 42559.0, "finishTime": 39184.447086}}, "tts": 73186, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184472171, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.8", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 311.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.399432, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 1.819, "sendEnd": 1.95, "receiveHeadersEnd": 48.367, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 73654, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184472361, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.8", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 2459}}, "tts": 73844, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184472641, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.8", "didFail": false, "encodedDataLength": 2788.0, "decodedBodyLength": 2459.0, "finishTime": 39184.448403}}, "tts": 74124, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184473379, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.6", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 314.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.398945, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 2.258, "sendEnd": 2.435, "receiveHeadersEnd": 50.089, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 74852, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184473557, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.6", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 3403}}, "tts": 75030, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184473765, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.6", "didFail": false, "encodedDataLength": 3735.0, "decodedBodyLength": 3403.0, "finishTime": 39184.44952}}, "tts": 75237, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184474259, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.7", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 376.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.399228, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 2.0, "sendEnd": 2.153, "receiveHeadersEnd": 69.992, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 75732, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184474568, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.4", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 7649}}, "tts": 76041, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184474671, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.4", "didFail": false, "encodedDataLength": 73597.0, "decodedBodyLength": 73185.0, "finishTime": 39184.447331}}, "tts": 76143, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184474946, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 9, "tdur": 8, "tts": 76419}, {"pid": 660, "tid": 660, "ts": 39184474977, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 3, "tdur": 3, "tts": 76450}, {"pid": 660, "tid": 660, "ts": 39184475001, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 2, "tdur": 2, "tts": 76473}, {"pid": 660, "tid": 660, "ts": 39184475902, "ph": "X", "cat": "devtools.timeline", "name": "EvaluateScript", "args": {"data": {"url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 1, "columnNumber": 1, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 4626, "tdur": 4627, "tts": 76712}, {"pid": 660, "tid": 660, "ts": 39184475909, "ph": "B", "cat": "v8,devtools.timeline", "name": "v8.compile", "args": {"fileName": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3"}, "tts": 76719}, {"pid": 660, "tid": 660, "ts": 39184476187, "ph": "E", "cat": "v8,devtools.timeline", "name": "v8.compile", "args": {"data": {"url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 1, "columnNumber": 1, "streamed": true}}, "tts": 76997}, {"pid": 660, "tid": 660, "ts": 39184480545, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 5, "tdur": 5, "tts": 81355}, {"pid": 660, "tid": 660, "ts": 39184480558, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 117, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 81367}, {"pid": 660, "tid": 660, "ts": 39184480588, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "readystatechange"}}, "dur": 2, "tdur": 2, "tts": 81398}, {"pid": 660, "tid": 660, "ts": 39184480597, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "DOMContentLoaded"}}, "dur": 1849, "tdur": 1849, "tts": 81407}, {"pid": 660, "tid": 660, "ts": 39184480726, "ph": "B", "cat": "devtools.timeline", "name": "FunctionCall", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "functionName": "document.addEventListener", "scriptId": "15", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 454, "columnNumber": 47}}, "tts": 81536}, {"pid": 660, "tid": 680, "ts": 39184471444, "ph": "X", "cat": "v8,devtools.timeline", "name": "v8.parseOnBackground", "args": {"data": {"requestId": "1000000660.9", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3"}}, "dur": 4245, "tdur": 3979, "tts": 212}, {"pid": 660, "tid": 660, "ts": 39184481680, "ph": "E", "cat": "devtools.timeline", "name": "FunctionCall", "args": {}, "tts": 82491}, {"pid": 660, "tid": 660, "ts": 39184481708, "ph": "B", "cat": "devtools.timeline", "name": "FunctionCall", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "functionName": "document.addEventListener", "scriptId": "15", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 503, "columnNumber": 47}}, "tts": 82518}, {"pid": 660, "tid": 660, "ts": 39184482177, "ph": "X", "cat": "devtools.timeline", "name": "XHRReadyStateChange", "args": {"data": {"url": "https://cristaldiamondofcanada.com/config/config.json", "readyState": 1, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 8, "tdur": 7, "tts": 82988}, {"pid": 660, "tid": 660, "ts": 39184482285, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "61"}}, "tts": 83095}, {"pid": 660, "tid": 660, "ts": 39184482296, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.61", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/config/config.json", "requestMethod": "GET", "priority": "High"}}, "tts": 83106, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184482357, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 83167}, {"pid": 660, "tid": 660, "ts": 39184482439, "ph": "E", "cat": "devtools.timeline", "name": "FunctionCall", "args": {}, "tts": 83249}, {"pid": 660, "tid": 660, "ts": 39184482455, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 83264}, {"pid": 660, "tid": 660, "ts": 39184482481, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 0}, "tts": 83291}, {"pid": 660, "tid": 660, "ts": 39184482502, "ph": "I", "cat": "devtools.timeline", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": {"data": {"isMainFrame": true, "page": "BFA6B40D1E4405E5925EEA32B23C3F03", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 83312, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184482519, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 83328}, {"pid": 660, "tid": 660, "ts": 39184489854, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.7", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 3403}}, "tts": 83691, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184490461, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.7", "didFail": false, "encodedDataLength": 3797.0, "decodedBodyLength": 3403.0, "finishTime": 39184.469744}}, "tts": 83952, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184490857, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.5", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 370.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.3986, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 2.575, "sendEnd": 2.779, "receiveHeadersEnd": 89.751, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 84306, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184490993, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.5", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 1752}}, "tts": 84442, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184491201, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.5", "didFail": false, "encodedDataLength": 2140.0, "decodedBodyLength": 1752.0, "finishTime": 39184.489017}}, "tts": 84627, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184491544, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 8, "tdur": 7, "tts": 84921}, {"pid": 660, "tid": 660, "ts": 39184491576, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 2, "tdur": 2, "tts": 84952}, {"pid": 660, "tid": 660, "ts": 39184497626, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.58", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/jpeg", "encodedDataLength": 304.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.451401, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.316, "sendEnd": 0.534, "receiveHeadersEnd": 42.008, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 85300, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184499691, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.58", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 65536}}, "tts": 85814, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184500193, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.59", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 293.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.451799, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.156, "sendEnd": 0.203, "receiveHeadersEnd": 46.598, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 86316, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184502159, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.52", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "application/font-woff", "encodedDataLength": 329.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.455134, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.116, "sendEnd": 0.18, "receiveHeadersEnd": 44.29, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 86806, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184502286, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.52", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 36800}}, "tts": 86933, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184502565, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.52", "didFail": false, "encodedDataLength": 37162.0, "decodedBodyLength": 36800.0, "finishTime": 39184.501779}}, "tts": 87161, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184504349, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.59", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 49474}}, "tts": 88945, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184504663, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.58", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 30448}}, "tts": 89259, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184505232, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.58", "didFail": false, "encodedDataLength": 96405.0, "decodedBodyLength": 95984.0, "finishTime": 39184.500715}}, "tts": 89829, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184506060, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.59", "didFail": false, "encodedDataLength": 49839.0, "decodedBodyLength": 49474.0, "finishTime": 39184.500911}}, "tts": 90656, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184510699, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 91077}, {"pid": 660, "tid": 660, "ts": 39184513498, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 38}, "tts": 92710}, {"pid": 660, "tid": 660, "ts": 39184513513, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 47, "totalObjects": 47, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 92724}, {"pid": 660, "tid": 660, "ts": 39184514508, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 93720}, {"pid": 660, "tid": 660, "ts": 39184514525, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 251, "tdur": 252, "tts": 93736}, {"pid": 660, "tid": 660, "ts": 39184514789, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 221, "tdur": 222, "tts": 94000}, {"pid": 660, "tid": 660, "ts": 39184515047, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [958.0, 953.0, 962.0, 953.0, 962.0, 955.0, 958.0, 955.0], "nodeId": 7, "layerId": 13}}, "dur": 40, "tdur": 40, "tts": 94258}, {"pid": 660, "tid": 660, "ts": 39184515100, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [959.0, 953.0, 959.0, 953.0, 959.0, 953.0, 959.0, 953.0], "nodeId": 9, "layerId": 15}}, "dur": 27, "tdur": 27, "tts": 94311}, {"pid": 660, "tid": 660, "ts": 39184515138, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [582.0, 403.0, 879.0, 403.0, 879.0, 570.0, 582.0, 570.0], "nodeId": 12, "layerId": 18}}, "dur": 11, "tdur": 12, "tts": 94349}, {"pid": 660, "tid": 660, "ts": 39184515512, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94627, "id": "0xaf627bd6d164b183"}, {"pid": 660, "tid": 660, "ts": 39184515518, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94632, "id": "0xaf627bd6d16498eb"}, {"pid": 660, "tid": 660, "ts": 39184515521, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94636, "id": "0xaf627bd6d1648dcb"}, {"pid": 660, "tid": 660, "ts": 39184515524, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94639, "id": "0xaf627bd6d164fed3"}, {"pid": 660, "tid": 660, "ts": 39184515527, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94642, "id": "0xaf627bd6d164f6bb"}, {"pid": 660, "tid": 660, "ts": 39184515531, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94645, "id": "0xaf627bd6d164ede3"}, {"pid": 660, "tid": 660, "ts": 39184515534, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 94649, "id": "0xaf627bd6d164d753"}, {"pid": 660, "tid": 660, "ts": 39184515818, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.46", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "application/font-woff", "encodedDataLength": 311.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.456448, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.119, "sendEnd": 0.176, "receiveHeadersEnd": 54.023, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 94912, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184516461, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.46", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 36924}}, "tts": 95082, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184516866, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.46", "didFail": false, "encodedDataLength": 37267.0, "decodedBodyLength": 36924.0, "finishTime": 39184.511842}}, "tts": 95346, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184520098, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.60", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "image/png", "encodedDataLength": 316.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.453329, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.199, "sendEnd": 0.349, "receiveHeadersEnd": 58.005, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 97407, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184523641, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.60", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 50295}}, "tts": 97572, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184525008, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.60", "didFail": false, "encodedDataLength": 50683.0, "decodedBodyLength": 50295.0, "finishTime": 39184.512364}}, "tts": 98185, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "<PERSON><PERSON><PERSON><PERSON>", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 98608}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "firstContentful<PERSON><PERSON>t", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 98627}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "firstTextPaint", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 98635}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "firstImage<PERSON><PERSON>t", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 98640}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "firstMeaningfulPaintCandidate", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 98645}, {"pid": 660, "tid": 660, "ts": 39184536945, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.42", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "application/font-woff", "encodedDataLength": 399.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.462025, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.133, "sendEnd": 0.205, "receiveHeadersEnd": 74.312, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 99351, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184538324, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.42", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 32768}}, "tts": 99832, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184538555, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.42", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 112}}, "tts": 99975, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184539006, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.42", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 2428}}, "tts": 100048, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184539487, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.42", "didFail": false, "encodedDataLength": 35664.0, "decodedBodyLength": 35308.0, "finishTime": 39184.539128}}, "tts": 100262, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184542051, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "readystatechange"}}, "dur": 7, "tdur": 7, "tts": 101749}, {"pid": 660, "tid": 660, "ts": 39184542068, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 2, "tdur": 3, "tts": 101765}, {"pid": 660, "tid": 660, "ts": 39184542080, "ph": "I", "cat": "devtools.timeline", "name": "MarkLoad", "args": {"data": {"isMainFrame": true, "page": "BFA6B40D1E4405E5925EEA32B23C3F03", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 101778, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184542092, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "pageshow"}}, "dur": 2, "tdur": 2, "tts": 101790}, {"pid": 660, "tid": 660, "ts": 39184542678, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 101814}, {"pid": 660, "tid": 660, "ts": 39184544388, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 38}, "tts": 103523}, {"pid": 660, "tid": 660, "ts": 39184544400, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 47, "totalObjects": 47, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 103535}, {"pid": 660, "tid": 660, "ts": 39184547166, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 106011}, {"pid": 660, "tid": 660, "ts": 39184550161, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.61", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "application/json", "encodedDataLength": 350.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.489655, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.501, "sendEnd": 0.746, "receiveHeadersEnd": 51.396, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 106781, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184550311, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.61", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 1334}}, "tts": 106930, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184550329, "ph": "X", "cat": "devtools.timeline", "name": "XHRReadyStateChange", "args": {"data": {"url": "https://cristaldiamondofcanada.com/config/config.json", "readyState": 2, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 169, "tdur": 169, "tts": 106948}, {"pid": 660, "tid": 660, "ts": 39184550376, "ph": "B", "cat": "devtools.timeline", "name": "FunctionCall", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "functionName": "rawFile.onreadystatechange", "scriptId": "15", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 663, "columnNumber": 42}}, "tts": 106995}, {"pid": 660, "tid": 660, "ts": 39184550489, "ph": "E", "cat": "devtools.timeline", "name": "FunctionCall", "args": {}, "tts": 107108}, {"pid": 660, "tid": 660, "ts": 39184550525, "ph": "X", "cat": "devtools.timeline", "name": "XHRReadyStateChange", "args": {"data": {"url": "https://cristaldiamondofcanada.com/config/config.json", "readyState": 3, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 52, "tdur": 51, "tts": 107144}, {"pid": 660, "tid": 660, "ts": 39184550540, "ph": "B", "cat": "devtools.timeline", "name": "FunctionCall", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "functionName": "rawFile.onreadystatechange", "scriptId": "15", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 663, "columnNumber": 42}}, "tts": 107159}, {"pid": 660, "tid": 660, "ts": 39184550573, "ph": "E", "cat": "devtools.timeline", "name": "FunctionCall", "args": {}, "tts": 107192}, {"pid": 660, "tid": 660, "ts": 39184550891, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.61", "didFail": false, "encodedDataLength": 1067.0, "decodedBodyLength": 1334.0, "finishTime": 39184.541618}}, "tts": 107381, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184550935, "ph": "X", "cat": "devtools.timeline", "name": "XHRReadyStateChange", "args": {"data": {"url": "https://cristaldiamondofcanada.com/config/config.json", "readyState": 4, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 1596, "tdur": 1596, "tts": 107425}, {"pid": 660, "tid": 660, "ts": 39184550960, "ph": "B", "cat": "devtools.timeline", "name": "FunctionCall", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "functionName": "rawFile.onreadystatechange", "scriptId": "15", "url": "https://cristaldiamondofcanada.com/js/app.min.js?version=1.3", "lineNumber": 663, "columnNumber": 42}}, "tts": 107450}, {"pid": 660, "tid": 660, "ts": 39184551507, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 107997}, {"pid": 660, "tid": 660, "ts": 39184551520, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108010}, {"pid": 660, "tid": 660, "ts": 39184551525, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108015}, {"pid": 660, "tid": 660, "ts": 39184551529, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108018}, {"pid": 660, "tid": 660, "ts": 39184551569, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108059}, {"pid": 660, "tid": 660, "ts": 39184551574, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108063}, {"pid": 660, "tid": 660, "ts": 39184551577, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108067}, {"pid": 660, "tid": 660, "ts": 39184551580, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108069}, {"pid": 660, "tid": 660, "ts": 39184551610, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108099}, {"pid": 660, "tid": 660, "ts": 39184551613, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108103}, {"pid": 660, "tid": 660, "ts": 39184551617, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108107}, {"pid": 660, "tid": 660, "ts": 39184551620, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108110}, {"pid": 660, "tid": 660, "ts": 39184551641, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108130}, {"pid": 660, "tid": 660, "ts": 39184551644, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108134}, {"pid": 660, "tid": 660, "ts": 39184551648, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108138}, {"pid": 660, "tid": 660, "ts": 39184551651, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108140}, {"pid": 660, "tid": 660, "ts": 39184551667, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108157}, {"pid": 660, "tid": 660, "ts": 39184551671, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108160}, {"pid": 660, "tid": 660, "ts": 39184551674, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108163}, {"pid": 660, "tid": 660, "ts": 39184551677, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108166}, {"pid": 660, "tid": 660, "ts": 39184551690, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108179}, {"pid": 660, "tid": 660, "ts": 39184551693, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108183}, {"pid": 660, "tid": 660, "ts": 39184551697, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108186}, {"pid": 660, "tid": 660, "ts": 39184551699, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108189}, {"pid": 660, "tid": 660, "ts": 39184552384, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108875}, {"pid": 660, "tid": 660, "ts": 39184552420, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108910}, {"pid": 660, "tid": 660, "ts": 39184552427, "ph": "B", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"beginData": {"startLine": 0, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/"}}, "tts": 108917}, {"pid": 660, "tid": 660, "ts": 39184552431, "ph": "E", "cat": "devtools.timeline", "name": "ParseHTML", "args": {"endData": {"endLine": -1}}, "tts": 108921}, {"pid": 660, "tid": 660, "ts": 39184552474, "ph": "I", "cat": "devtools.timeline", "name": "TimerInstall", "args": {"data": {"timerId": 1, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "timeout": 40000, "singleShot": true}}, "tts": 108964, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184552521, "ph": "E", "cat": "devtools.timeline", "name": "FunctionCall", "args": {}, "tts": 109011}, {"pid": 660, "tid": 660, "ts": 39184552537, "ph": "X", "cat": "devtools.timeline", "name": "XHRLoad", "args": {"data": {"url": "https://cristaldiamondofcanada.com/config/config.json", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 6, "tdur": 5, "tts": 109027}, {"pid": 660, "tid": 660, "ts": 39184553256, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "load"}}, "dur": 7, "tdur": 5, "tts": 109371}, {"pid": 660, "tid": 660, "ts": 39184602087, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 109840}, {"pid": 660, "tid": 660, "ts": 39184602714, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "8", "state": "pending", "name": "", "nodeId": 13, "nodeName": "BUTTON id='openButton' class='sl-game-button sl-game__openButton disabled'"}}, "tts": 110467, "id": "0xaf627bd6d16747a3"}, {"pid": 660, "tid": 660, "ts": 39184602757, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "9", "state": "pending", "name": "", "nodeId": 13, "nodeName": "BUTTON id='openButton' class='sl-game-button sl-game__openButton disabled'"}}, "tts": 110509, "id": "0xaf627bd6d16745bb"}, {"pid": 660, "tid": 660, "ts": 39184604098, "ph": "B", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {"data": {"id": "56"}}, "tts": 111004}, {"pid": 660, "tid": 660, "ts": 39184604111, "ph": "I", "cat": "devtools.timeline", "name": "ResourceSendRequest", "args": {"data": {"requestId": "1000000660.56", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "url": "https://cristaldiamondofcanada.com/fonts/<PERSON><PERSON>-<PERSON>.woff", "requestMethod": "GET", "priority": "VeryHigh"}}, "tts": 111017, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184604152, "ph": "E", "cat": "devtools.timeline", "name": "PlatformResourceSendRequest", "args": {}, "tts": 111058}, {"pid": 660, "tid": 660, "ts": 39184605761, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 17}, "tts": 111858}, {"pid": 660, "tid": 660, "ts": 39184605774, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 20, "totalObjects": 55, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 111870}, {"pid": 660, "tid": 660, "ts": 39184610881, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 112965}, {"pid": 660, "tid": 660, "ts": 39184610903, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 409, "tdur": 409, "tts": 112986}, {"pid": 660, "tid": 660, "ts": 39184611324, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 313, "tdur": 314, "tts": 113407}, {"pid": 660, "tid": 660, "ts": 39184611675, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [943.0, 787.0, 982.0, 787.0, 982.0, 806.0, 943.0, 806.0], "nodeId": 7, "layerId": 13}}, "dur": 20, "tdur": 20, "tts": 113758}, {"pid": 660, "tid": 660, "ts": 39184611707, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [943.0, 786.0, 977.0, 786.0, 977.0, 800.0, 943.0, 800.0], "nodeId": 8, "layerId": 14}}, "dur": 10, "tdur": 10, "tts": 113790}, {"pid": 660, "tid": 660, "ts": 39184611726, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [952.0, 787.0, 951.0, 787.0, 951.0, 786.0, 952.0, 786.0], "nodeId": 9, "layerId": 15}}, "dur": 34, "tdur": 35, "tts": 113808}, {"pid": 660, "tid": 660, "ts": 39184611771, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [582.0, 243.0, 879.0, 243.0, 879.0, 410.0, 582.0, 410.0], "nodeId": 12, "layerId": 18}}, "dur": 45, "tdur": 45, "tts": 113854}, {"pid": 660, "tid": 660, "ts": 39184643341, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceiveResponse", "args": {"data": {"requestId": "1000000660.56", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "statusCode": 200, "mimeType": "application/font-woff", "encodedDataLength": 331.0, "fromCache": false, "fromServiceWorker": false, "timing": {"requestTime": 39184.605276, "proxyStart": -1.0, "proxyEnd": -1.0, "dnsStart": -1.0, "dnsEnd": -1.0, "connectStart": -1.0, "connectEnd": -1.0, "sslStart": -1.0, "sslEnd": -1.0, "workerStart": -1.0, "workerReady": -1.0, "sendStart": 0.236, "sendEnd": 0.391, "receiveHeadersEnd": 35.823, "pushStart": 0.0, "pushEnd": 0.0}}}, "tts": 114808, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184643449, "ph": "I", "cat": "devtools.timeline", "name": "ResourceReceivedData", "args": {"data": {"requestId": "1000000660.56", "frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "encodedDataLength": 36708}}, "tts": 114916, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184643632, "ph": "I", "cat": "devtools.timeline", "name": "ResourceFinish", "args": {"data": {"requestId": "1000000660.56", "didFail": false, "encodedDataLength": 37069.0, "decodedBodyLength": 36708.0, "finishTime": 39184.642958}}, "tts": 115099, "s": "t"}, {"pid": 660, "tid": 660, "ts": 39184644855, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 116322}, {"pid": 660, "tid": 660, "ts": 39184645997, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 40}, "tts": 117464}, {"pid": 660, "tid": 660, "ts": 39184646008, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 55, "totalObjects": 55, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 117475}, {"pid": 660, "tid": 660, "ts": 39184647234, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 118701}, {"pid": 660, "tid": 660, "ts": 39184647252, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 284, "tdur": 283, "tts": 118719}, {"pid": 660, "tid": 660, "ts": 39184647548, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 225, "tdur": 225, "tts": 119015}, {"pid": 660, "tid": 660, "ts": 39184647806, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [934.0, 783.0, 995.0, 783.0, 995.0, 813.0, 934.0, 813.0], "nodeId": 7, "layerId": 13}}, "dur": 22, "tdur": 21, "tts": 119273}, {"pid": 660, "tid": 660, "ts": 39184647838, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [934.0, 783.0, 986.0, 783.0, 986.0, 804.0, 934.0, 804.0], "nodeId": 8, "layerId": 14}}, "dur": 33, "tdur": 32, "tts": 119305}, {"pid": 660, "tid": 660, "ts": 39184647886, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [948.0, 783.0, 946.0, 784.0, 946.0, 782.0, 948.0, 782.0], "nodeId": 9, "layerId": 15}}, "dur": 37, "tdur": 37, "tts": 119353}, {"pid": 660, "tid": 660, "ts": 39184647939, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [582.0, 243.0, 879.0, 243.0, 879.0, 410.0, 582.0, 410.0], "nodeId": 12, "layerId": 18}}, "dur": 58, "tdur": 58, "tts": 119406}, {"pid": 660, "tid": 660, "ts": 39184649008, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 119737, "id": "0xaf627bd6d16747a3"}, {"pid": 660, "tid": 660, "ts": 39184649014, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 119742, "id": "0xaf627bd6d16745bb"}, {"pid": 660, "tid": 660, "ts": 39184669935, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 120368}, {"pid": 660, "tid": 660, "ts": 39184670984, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "10", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 121328, "id": "0xaf627bd6d167bd1b"}, {"pid": 660, "tid": 660, "ts": 39184671677, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 121991}, {"pid": 660, "tid": 660, "ts": 39184671691, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 7, "totalObjects": 55, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 122004}, {"pid": 660, "tid": 660, "ts": 39184671960, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 122274}, {"pid": 660, "tid": 660, "ts": 39184672001, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 661, "tdur": 662, "tts": 122314}, {"pid": 660, "tid": 660, "ts": 39184672195, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 122508, "id": "0xaf627bd6d167bd1b"}, {"pid": 660, "tid": 660, "ts": 39184672679, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 122992}, {"pid": 660, "tid": 660, "ts": 39184684422, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 127549}, {"pid": 660, "tid": 660, "ts": 39184685580, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 128676, "id": "0xaf627bd6d167bd1b"}, {"pid": 660, "tid": 660, "ts": 39184685606, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "11", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 128700, "id": "0xaf627bd6d167b92b"}, {"pid": 660, "tid": 660, "ts": 39184685897, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 128992}, {"pid": 660, "tid": 660, "ts": 39184685913, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 247, "tdur": 247, "tts": 129007}, {"pid": 660, "tid": 660, "ts": 39184686080, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 129174, "id": "0xaf627bd6d167b92b"}, {"pid": 660, "tid": 660, "ts": 39184686171, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 57, "tdur": 56, "tts": 129266}, {"pid": 660, "tid": 660, "ts": 39184687284, "ph": "B", "cat": "blink_gc,devtools.timeline", "name": "BlinkGC.AtomicPhase", "args": {}, "tts": 129530}, {"pid": 660, "tid": 660, "ts": 39184692123, "ph": "E", "cat": "blink_gc,devtools.timeline", "name": "BlinkGC.AtomicPhase", "args": {}, "tts": 134368}, {"pid": 660, "tid": 660, "ts": 39184700597, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 134656}, {"pid": 660, "tid": 660, "ts": 39184701348, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 135369, "id": "0xaf627bd6d167b92b"}, {"pid": 660, "tid": 660, "ts": 39184701369, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "12", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 135389, "id": "0xaf627bd6d167b53b"}, {"pid": 660, "tid": 660, "ts": 39184701567, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 135587}, {"pid": 660, "tid": 660, "ts": 39184701581, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 237, "tdur": 237, "tts": 135601}, {"pid": 660, "tid": 660, "ts": 39184701689, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 135709, "id": "0xaf627bd6d167b53b"}, {"pid": 660, "tid": 660, "ts": 39184701832, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 135852}, {"pid": 660, "tid": 660, "ts": 39184718183, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 137725}, {"pid": 660, "tid": 660, "ts": 39184718753, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 138264, "id": "0xaf627bd6d167b53b"}, {"pid": 660, "tid": 660, "ts": 39184718815, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "13", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 138325, "id": "0xaf627bd6d167b0cb"}, {"pid": 660, "tid": 660, "ts": 39184719035, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 138545}, {"pid": 660, "tid": 660, "ts": 39184719049, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 179, "tdur": 180, "tts": 138559}, {"pid": 660, "tid": 660, "ts": 39184719157, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 138667, "id": "0xaf627bd6d167b0cb"}, {"pid": 660, "tid": 660, "ts": 39184719240, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 57, "tdur": 57, "tts": 138750}, {"pid": 660, "tid": 660, "ts": 39184733686, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 139141}, {"pid": 660, "tid": 660, "ts": 39184735095, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 140428, "id": "0xaf627bd6d167b0cb"}, {"pid": 660, "tid": 660, "ts": 39184735118, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "14", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 140450, "id": "0xaf627bd6d167acdb"}, {"pid": 660, "tid": 660, "ts": 39184735439, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 140771}, {"pid": 660, "tid": 660, "ts": 39184735456, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 301, "tdur": 301, "tts": 140788}, {"pid": 660, "tid": 660, "ts": 39184735656, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 140988, "id": "0xaf627bd6d167acdb"}, {"pid": 660, "tid": 660, "ts": 39184735809, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 80, "tdur": 80, "tts": 141141}, {"pid": 660, "tid": 660, "ts": 39184750301, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 141675}, {"pid": 660, "tid": 660, "ts": 39184751412, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 142785, "id": "0xaf627bd6d167acdb"}, {"pid": 660, "tid": 660, "ts": 39184751437, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "15", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 142810, "id": "0xaf627bd6d167a8eb"}, {"pid": 660, "tid": 660, "ts": 39184752539, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 143913}, {"pid": 660, "tid": 660, "ts": 39184752561, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 403, "tdur": 403, "tts": 143934}, {"pid": 660, "tid": 660, "ts": 39184752825, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 144199, "id": "0xaf627bd6d167a8eb"}, {"pid": 660, "tid": 660, "ts": 39184752988, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 145, "tdur": 145, "tts": 144361}, {"pid": 660, "tid": 660, "ts": 39184767411, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 144902}, {"pid": 660, "tid": 660, "ts": 39184768084, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 145574, "id": "0xaf627bd6d167a8eb"}, {"pid": 660, "tid": 660, "ts": 39184768113, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "16", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 145602, "id": "0xaf627bd6d167a4fb"}, {"pid": 660, "tid": 660, "ts": 39184768566, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 145976}, {"pid": 660, "tid": 660, "ts": 39184768587, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 695, "tdur": 694, "tts": 145996}, {"pid": 660, "tid": 660, "ts": 39184769144, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 146553, "id": "0xaf627bd6d167a4fb"}, {"pid": 660, "tid": 660, "ts": 39184769298, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 92, "tdur": 92, "tts": 146707}, {"pid": 660, "tid": 660, "ts": 39184783529, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 147167}, {"pid": 660, "tid": 660, "ts": 39184783965, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 147602, "id": "0xaf627bd6d167a4fb"}, {"pid": 660, "tid": 660, "ts": 39184783987, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "17", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 147624, "id": "0xaf627bd6d167a08b"}, {"pid": 660, "tid": 660, "ts": 39184784682, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 148320}, {"pid": 660, "tid": 660, "ts": 39184784699, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 873, "tdur": 874, "tts": 148336}, {"pid": 660, "tid": 660, "ts": 39184785380, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 149018, "id": "0xaf627bd6d167a08b"}, {"pid": 660, "tid": 660, "ts": 39184785590, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 149227}, {"pid": 660, "tid": 660, "ts": 39184800399, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 149680}, {"pid": 660, "tid": 660, "ts": 39184800988, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 150269, "id": "0xaf627bd6d167a08b"}, {"pid": 660, "tid": 660, "ts": 39184801014, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "18", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 150294, "id": "0xaf627bd6d1679c9b"}, {"pid": 660, "tid": 660, "ts": 39184801312, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 150593}, {"pid": 660, "tid": 660, "ts": 39184801334, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 306, "tdur": 308, "tts": 150613}, {"pid": 660, "tid": 660, "ts": 39184801509, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 150790, "id": "0xaf627bd6d1679c9b"}, {"pid": 660, "tid": 660, "ts": 39184801660, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 92, "tdur": 92, "tts": 150940}, {"pid": 660, "tid": 660, "ts": 39184817280, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 151469}, {"pid": 660, "tid": 660, "ts": 39184817766, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "idle"}}, "tts": 151956, "id": "0xaf627bd6d1679c9b"}, {"pid": 660, "tid": 660, "ts": 39184817871, "ph": "b", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"id": "19", "state": "pending", "name": "", "nodeId": 6, "nodeName": "I class='splash'"}}, "tts": 152060, "id": "0xaf627bd6d16798ab"}, {"pid": 660, "tid": 660, "ts": 39184818274, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 13}, "tts": 152463}, {"pid": 660, "tid": 660, "ts": 39184818295, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 658, "tdur": 659, "tts": 152483}, {"pid": 660, "tid": 660, "ts": 39184818836, "ph": "n", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"data": {"state": "running"}}, "tts": 153025, "id": "0xaf627bd6d16798ab"}, {"pid": 660, "tid": 660, "ts": 39184818972, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 825, "tdur": 826, "tts": 153161}, {"pid": 660, "tid": 660, "ts": 39184833633, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 154390}, {"pid": 660, "tid": 660, "ts": 39184834568, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 12}, "tts": 155324}, {"pid": 660, "tid": 660, "ts": 39184834591, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 874, "tdur": 876, "tts": 155346}, {"pid": 660, "tid": 660, "ts": 39184835487, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 84, "tdur": 84, "tts": 156243}, {"pid": 660, "tid": 660, "ts": 39184850371, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "finished"}}, "tts": 156797, "id": "0xaf627bd6d16747a3"}, {"pid": 660, "tid": 660, "ts": 39184850380, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "finished"}}, "tts": 156805, "id": "0xaf627bd6d16745bb"}, {"pid": 660, "tid": 660, "ts": 39184850415, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 156840}, {"pid": 660, "tid": 660, "ts": 39184851852, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 12}, "tts": 158279}, {"pid": 660, "tid": 660, "ts": 39184851874, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 410, "tdur": 410, "tts": 158300}, {"pid": 660, "tid": 660, "ts": 39184852889, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 119, "tdur": 119, "tts": 159315}, {"pid": 660, "tid": 660, "ts": 39184866988, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 159953}, {"pid": 660, "tid": 660, "ts": 39184867470, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 160435}, {"pid": 660, "tid": 660, "ts": 39184867488, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 481, "tdur": 481, "tts": 160453}, {"pid": 660, "tid": 660, "ts": 39184867991, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 88, "tdur": 87, "tts": 160956}, {"pid": 660, "tid": 660, "ts": 39184885006, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 161570}, {"pid": 660, "tid": 660, "ts": 39184885551, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 162115}, {"pid": 660, "tid": 660, "ts": 39184885575, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 610, "tdur": 518, "tts": 162137}, {"pid": 660, "tid": 660, "ts": 39184886204, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 72, "tdur": 72, "tts": 162673}, {"pid": 660, "tid": 660, "ts": 39184901694, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 163144}, {"pid": 660, "tid": 660, "ts": 39184902869, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 164319}, {"pid": 660, "tid": 660, "ts": 39184902891, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 522, "tdur": 522, "tts": 164340}, {"pid": 660, "tid": 660, "ts": 39184903435, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 96, "tdur": 95, "tts": 164884}, {"pid": 660, "tid": 660, "ts": 39184917268, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 165423}, {"pid": 660, "tid": 660, "ts": 39184917681, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 165835}, {"pid": 660, "tid": 660, "ts": 39184917701, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 283, "tdur": 283, "tts": 165854}, {"pid": 660, "tid": 660, "ts": 39184918003, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 90, "tdur": 91, "tts": 166155}, {"pid": 660, "tid": 660, "ts": 39184933294, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 166658}, {"pid": 660, "tid": 660, "ts": 39184933696, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 167059}, {"pid": 660, "tid": 660, "ts": 39184933716, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 301, "tdur": 302, "tts": 167078}, {"pid": 660, "tid": 660, "ts": 39184934037, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 86, "tdur": 85, "tts": 167400}, {"pid": 660, "tid": 660, "ts": 39184951237, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 167891}, {"pid": 660, "tid": 660, "ts": 39184951680, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 168334}, {"pid": 660, "tid": 660, "ts": 39184951701, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 289, "tdur": 290, "tts": 168354}, {"pid": 660, "tid": 660, "ts": 39184952013, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 91, "tdur": 91, "tts": 168666}, {"pid": 660, "tid": 660, "ts": 39184967267, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 169193}, {"pid": 660, "tid": 660, "ts": 39184967695, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 169621}, {"pid": 660, "tid": 660, "ts": 39184967715, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 284, "tdur": 284, "tts": 169640}, {"pid": 660, "tid": 660, "ts": 39184968020, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 90, "tdur": 89, "tts": 169946}, {"pid": 660, "tid": 660, "ts": 39184983291, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 170439}, {"pid": 660, "tid": 660, "ts": 39184983714, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 170862}, {"pid": 660, "tid": 660, "ts": 39184983735, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 280, "tdur": 280, "tts": 170882}, {"pid": 660, "tid": 660, "ts": 39184984035, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 91, "tdur": 90, "tts": 171182}, {"pid": 660, "tid": 660, "ts": 39185000418, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 171699}, {"pid": 660, "tid": 660, "ts": 39185001051, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 172331}, {"pid": 660, "tid": 660, "ts": 39185001071, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 290, "tdur": 291, "tts": 172350}, {"pid": 660, "tid": 660, "ts": 39185001381, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 89, "tdur": 89, "tts": 172661}, {"pid": 660, "tid": 660, "ts": 39185016712, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 173213}, {"pid": 660, "tid": 660, "ts": 39185017208, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 173708}, {"pid": 660, "tid": 660, "ts": 39185017229, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 278, "tdur": 279, "tts": 173728}, {"pid": 660, "tid": 660, "ts": 39185017526, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 92, "tdur": 92, "tts": 174025}, {"pid": 660, "tid": 660, "ts": 39185033783, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 174525}, {"pid": 660, "tid": 660, "ts": 39185034207, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 174949}, {"pid": 660, "tid": 660, "ts": 39185034227, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 271, "tdur": 271, "tts": 174969}, {"pid": 660, "tid": 660, "ts": 39185034517, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 87, "tdur": 87, "tts": 175258}, {"pid": 660, "tid": 660, "ts": 39185050035, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 175763}, {"pid": 660, "tid": 660, "ts": 39185050485, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 176213}, {"pid": 660, "tid": 660, "ts": 39185050508, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 357, "tdur": 326, "tts": 176235}, {"pid": 660, "tid": 660, "ts": 39185050896, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 95, "tdur": 95, "tts": 176582}, {"pid": 660, "tid": 660, "ts": 39185067456, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 177096}, {"pid": 660, "tid": 660, "ts": 39185067923, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 177563}, {"pid": 660, "tid": 660, "ts": 39185067951, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 435, "tdur": 336, "tts": 177589}, {"pid": 660, "tid": 660, "ts": 39185068468, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 246, "tdur": 246, "tts": 178007}, {"pid": 660, "tid": 660, "ts": 39185084542, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 178977}, {"pid": 660, "tid": 660, "ts": 39185085144, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 179560}, {"pid": 660, "tid": 660, "ts": 39185085172, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 361, "tdur": 322, "tts": 179586}, {"pid": 660, "tid": 660, "ts": 39185085556, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 101, "tdur": 101, "tts": 179931}, {"pid": 660, "tid": 660, "ts": 39185101119, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 180496}, {"pid": 660, "tid": 660, "ts": 39185101602, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 180978}, {"pid": 660, "tid": 660, "ts": 39185101626, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 368, "tdur": 327, "tts": 181001}, {"pid": 660, "tid": 660, "ts": 39185102014, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 101, "tdur": 101, "tts": 181348}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}, "tts": 181798}, {"pid": 660, "tid": 660, "ts": 39184512530, "ph": "R", "cat": "loading,rail,devtools.timeline", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "afterUserInput": 0}, "tts": 181830}, {"pid": 660, "tid": 660, "ts": 39185117737, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 181997}, {"pid": 660, "tid": 660, "ts": 39185118260, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 182522}, {"pid": 660, "tid": 660, "ts": 39185118294, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 363, "tdur": 322, "tts": 182554}, {"pid": 660, "tid": 660, "ts": 39185118675, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 76, "tdur": 76, "tts": 182894}, {"pid": 660, "tid": 660, "ts": 39185133808, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 183549}, {"pid": 660, "tid": 660, "ts": 39185134269, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 184010}, {"pid": 660, "tid": 660, "ts": 39185134292, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 456, "tdur": 415, "tts": 184032}, {"pid": 660, "tid": 660, "ts": 39185134771, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 93, "tdur": 93, "tts": 184469}, {"pid": 660, "tid": 660, "ts": 39185150590, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 184959}, {"pid": 660, "tid": 660, "ts": 39185152387, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 186694}, {"pid": 660, "tid": 660, "ts": 39185152410, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 287, "tdur": 288, "tts": 186714}, {"pid": 660, "tid": 660, "ts": 39185152719, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 242, "tdur": 243, "tts": 187023}, {"pid": 660, "tid": 660, "ts": 39185168920, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 187848}, {"pid": 660, "tid": 660, "ts": 39185169412, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 188340}, {"pid": 660, "tid": 660, "ts": 39185169437, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 374, "tdur": 375, "tts": 188363}, {"pid": 660, "tid": 660, "ts": 39185169832, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 101, "tdur": 100, "tts": 188759}, {"pid": 660, "tid": 660, "ts": 39185183395, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 189373}, {"pid": 660, "tid": 660, "ts": 39185184367, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 190279}, {"pid": 660, "tid": 660, "ts": 39185184388, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 267, "tdur": 267, "tts": 190299}, {"pid": 660, "tid": 660, "ts": 39185184672, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 102, "tdur": 101, "tts": 190583}, {"pid": 660, "tid": 660, "ts": 39185201218, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 191357}, {"pid": 660, "tid": 660, "ts": 39185201954, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 192092}, {"pid": 660, "tid": 660, "ts": 39185201973, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 259, "tdur": 260, "tts": 192110}, {"pid": 660, "tid": 660, "ts": 39185202249, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 73, "tdur": 72, "tts": 192387}, {"pid": 660, "tid": 660, "ts": 39185217270, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 193177}, {"pid": 660, "tid": 660, "ts": 39185217754, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 193661}, {"pid": 660, "tid": 660, "ts": 39185217773, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 292, "tdur": 294, "tts": 193679}, {"pid": 660, "tid": 660, "ts": 39185218087, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 88, "tdur": 89, "tts": 193993}, {"pid": 660, "tid": 660, "ts": 39185233429, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "finished"}}, "tts": 194458, "id": "0xaf627bd6d16798ab"}, {"pid": 660, "tid": 660, "ts": 39185233466, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 194494}, {"pid": 660, "tid": 660, "ts": 39185233913, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 194941}, {"pid": 660, "tid": 660, "ts": 39185233932, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 249, "tdur": 250, "tts": 194959}, {"pid": 660, "tid": 660, "ts": 39185234197, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 74, "tdur": 73, "tts": 195225}, {"pid": 660, "tid": 660, "ts": 39185250490, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 195735}, {"pid": 660, "tid": 660, "ts": 39185250914, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 196159}, {"pid": 660, "tid": 660, "ts": 39185250949, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 257, "tdur": 258, "tts": 196193}, {"pid": 660, "tid": 660, "ts": 39185251223, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 73, "tdur": 73, "tts": 196467}, {"pid": 660, "tid": 660, "ts": 39185300124, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 197295}, {"pid": 660, "tid": 660, "ts": 39185300431, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 197601}, {"pid": 660, "tid": 660, "ts": 39185300446, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 210, "tdur": 211, "tts": 197615}, {"pid": 660, "tid": 660, "ts": 39185300669, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 59, "tts": 197839}, {"pid": 660, "tid": 660, "ts": 39185317095, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 198727}, {"pid": 660, "tid": 660, "ts": 39185317610, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 199242}, {"pid": 660, "tid": 660, "ts": 39185317627, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 350, "tdur": 351, "tts": 199257}, {"pid": 660, "tid": 660, "ts": 39185317993, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 68, "tdur": 68, "tts": 199624}, {"pid": 660, "tid": 660, "ts": 39185333574, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 200129}, {"pid": 660, "tid": 660, "ts": 39185333860, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 200414}, {"pid": 660, "tid": 660, "ts": 39185333874, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 299, "tdur": 299, "tts": 200428}, {"pid": 660, "tid": 660, "ts": 39185334189, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 58, "tdur": 58, "tts": 200743}, {"pid": 660, "tid": 660, "ts": 39185349797, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 201181}, {"pid": 660, "tid": 660, "ts": 39185350098, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 201481}, {"pid": 660, "tid": 660, "ts": 39185350113, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 201, "tdur": 201, "tts": 201495}, {"pid": 660, "tid": 660, "ts": 39185350329, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 57, "tdur": 57, "tts": 201711}, {"pid": 660, "tid": 660, "ts": 39185367241, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 202152}, {"pid": 660, "tid": 660, "ts": 39185367534, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 202444}, {"pid": 660, "tid": 660, "ts": 39185367548, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 235, "tdur": 236, "tts": 202457}, {"pid": 660, "tid": 660, "ts": 39185367796, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 59, "tts": 202706}, {"pid": 660, "tid": 660, "ts": 39185383429, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 203286}, {"pid": 660, "tid": 660, "ts": 39185383824, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 203680}, {"pid": 660, "tid": 660, "ts": 39185383840, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 1070, "tdur": 1063, "tts": 203695}, {"pid": 660, "tid": 660, "ts": 39185384928, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 62, "tts": 204776}, {"pid": 660, "tid": 660, "ts": 39185399628, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 205367}, {"pid": 660, "tid": 660, "ts": 39185399927, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 205666}, {"pid": 660, "tid": 660, "ts": 39185399943, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 236, "tdur": 236, "tts": 205681}, {"pid": 660, "tid": 660, "ts": 39185400192, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 58, "tdur": 58, "tts": 205930}, {"pid": 660, "tid": 660, "ts": 39185416632, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 206381}, {"pid": 660, "tid": 660, "ts": 39185417048, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 206796}, {"pid": 660, "tid": 660, "ts": 39185417065, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 362, "tdur": 362, "tts": 206812}, {"pid": 660, "tid": 660, "ts": 39185417442, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 207189}, {"pid": 660, "tid": 660, "ts": 39185433924, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 207706}, {"pid": 660, "tid": 660, "ts": 39185434212, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 207993}, {"pid": 660, "tid": 660, "ts": 39185434225, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 310, "tdur": 311, "tts": 208006}, {"pid": 660, "tid": 660, "ts": 39185434550, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 59, "tts": 208332}, {"pid": 660, "tid": 660, "ts": 39185449882, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 208799}, {"pid": 660, "tid": 660, "ts": 39185450186, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 209102}, {"pid": 660, "tid": 660, "ts": 39185450199, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 282, "tdur": 282, "tts": 209115}, {"pid": 660, "tid": 660, "ts": 39185450496, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 67, "tdur": 67, "tts": 209412}, {"pid": 660, "tid": 660, "ts": 39185466734, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 209851}, {"pid": 660, "tid": 660, "ts": 39185467027, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 210143}, {"pid": 660, "tid": 660, "ts": 39185467041, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 344, "tdur": 409, "tts": 210157}, {"pid": 660, "tid": 660, "ts": 39185467472, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 66, "tdur": 66, "tts": 210587}, {"pid": 660, "tid": 660, "ts": 39185483840, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 211114}, {"pid": 660, "tid": 660, "ts": 39185484278, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 211534}, {"pid": 660, "tid": 660, "ts": 39185484294, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 200, "tdur": 200, "tts": 211550}, {"pid": 660, "tid": 660, "ts": 39185484507, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 58, "tdur": 57, "tts": 211763}, {"pid": 660, "tid": 660, "ts": 39185500039, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 212197}, {"pid": 660, "tid": 660, "ts": 39185500331, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 212487}, {"pid": 660, "tid": 660, "ts": 39185500345, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 212, "tdur": 212, "tts": 212501}, {"pid": 660, "tid": 660, "ts": 39185500571, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 58, "tdur": 58, "tts": 212727}, {"pid": 660, "tid": 660, "ts": 39185517279, "ph": "e", "cat": "blink.animations,devtools.timeline,benchmark,rail", "name": "Animation", "args": {"endData": {"state": "finished"}}, "tts": 213344, "id": "0xaf627bd6d164fed3"}, {"pid": 660, "tid": 660, "ts": 39185517308, "ph": "X", "cat": "devtools.timeline", "name": "EventDispatch", "args": {"data": {"type": "animationend"}}, "dur": 6, "tdur": 6, "tts": 213371}, {"pid": 660, "tid": 660, "ts": 39185517333, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 213396}, {"pid": 660, "tid": 660, "ts": 39185517740, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 11}, "tts": 213804}, {"pid": 660, "tid": 660, "ts": 39185517754, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 9, "totalObjects": 55, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 213817}, {"pid": 660, "tid": 660, "ts": 39185517891, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 213954}, {"pid": 660, "tid": 660, "ts": 39185517904, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 232, "tdur": 232, "tts": 213967}, {"pid": 660, "tid": 660, "ts": 39185518160, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 80, "tdur": 80, "tts": 214223}, {"pid": 660, "tid": 660, "ts": 39185518268, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [797.0, 724.0, 1123.0, 724.0, 1123.0, 857.0, 797.0, 857.0], "nodeId": 8, "layerId": 14}}, "dur": 26, "tdur": 26, "tts": 214331}, {"pid": 660, "tid": 660, "ts": 39185518305, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [882.0, 723.0, 929.0, 766.0, 886.0, 814.0, 839.0, 771.0], "nodeId": 9, "layerId": 15}}, "dur": 14, "tdur": 14, "tts": 214368}, {"pid": 660, "tid": 660, "ts": 39185533409, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 214810}, {"pid": 660, "tid": 660, "ts": 39185533722, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 215122}, {"pid": 660, "tid": 660, "ts": 39185533737, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 206, "tts": 215137}, {"pid": 660, "tid": 660, "ts": 39185533958, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 63, "tts": 215357}, {"pid": 660, "tid": 660, "ts": 39185550666, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 215861}, {"pid": 660, "tid": 660, "ts": 39185550924, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 216120}, {"pid": 660, "tid": 660, "ts": 39185550938, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 207, "tdur": 207, "tts": 216133}, {"pid": 660, "tid": 660, "ts": 39185551159, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 59, "tts": 216354}, {"pid": 660, "tid": 660, "ts": 39185569061, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 216855}, {"pid": 660, "tid": 660, "ts": 39185569326, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 217119}, {"pid": 660, "tid": 660, "ts": 39185569339, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 215, "tdur": 215, "tts": 217132}, {"pid": 660, "tid": 660, "ts": 39185569570, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 74, "tts": 217363}, {"pid": 660, "tid": 660, "ts": 39185583182, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 217941}, {"pid": 660, "tid": 660, "ts": 39185583538, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 218297}, {"pid": 660, "tid": 660, "ts": 39185583557, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 278, "tdur": 278, "tts": 218315}, {"pid": 660, "tid": 660, "ts": 39185583853, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 92, "tdur": 92, "tts": 218611}, {"pid": 660, "tid": 660, "ts": 39185617115, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 219160}, {"pid": 660, "tid": 660, "ts": 39185617378, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 219422}, {"pid": 660, "tid": 660, "ts": 39185617392, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 273, "tdur": 273, "tts": 219436}, {"pid": 660, "tid": 660, "ts": 39185617681, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 241, "tdur": 242, "tts": 219725}, {"pid": 660, "tid": 660, "ts": 39185633952, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 220375}, {"pid": 660, "tid": 660, "ts": 39185634213, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 220635}, {"pid": 660, "tid": 660, "ts": 39185634226, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 279, "tdur": 280, "tts": 220648}, {"pid": 660, "tid": 660, "ts": 39185634524, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 220946}, {"pid": 660, "tid": 660, "ts": 39185649871, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 221389}, {"pid": 660, "tid": 660, "ts": 39185650230, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 221747}, {"pid": 660, "tid": 660, "ts": 39185650246, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 213, "tdur": 213, "tts": 221762}, {"pid": 660, "tid": 660, "ts": 39185650476, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 221992}, {"pid": 660, "tid": 660, "ts": 39185666769, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 222444}, {"pid": 660, "tid": 660, "ts": 39185667032, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 222707}, {"pid": 660, "tid": 660, "ts": 39185667047, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 290, "tdur": 290, "tts": 222721}, {"pid": 660, "tid": 660, "ts": 39185667352, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 223026}, {"pid": 660, "tid": 660, "ts": 39185683994, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 223583}, {"pid": 660, "tid": 660, "ts": 39185684259, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 223848}, {"pid": 660, "tid": 660, "ts": 39185684274, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 188, "tdur": 188, "tts": 223862}, {"pid": 660, "tid": 660, "ts": 39185684486, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 61, "tts": 224075}, {"pid": 660, "tid": 660, "ts": 39185700304, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 224604}, {"pid": 660, "tid": 660, "ts": 39185700566, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 224865}, {"pid": 660, "tid": 660, "ts": 39185700580, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 210, "tdur": 210, "tts": 224879}, {"pid": 660, "tid": 660, "ts": 39185700803, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 225102}, {"pid": 660, "tid": 660, "ts": 39185716709, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 225513}, {"pid": 660, "tid": 660, "ts": 39185717003, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 225805}, {"pid": 660, "tid": 660, "ts": 39185717017, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 186, "tdur": 186, "tts": 225819}, {"pid": 660, "tid": 660, "ts": 39185717217, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 61, "tts": 226019}, {"pid": 660, "tid": 660, "ts": 39185733276, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 226506}, {"pid": 660, "tid": 660, "ts": 39185733800, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 227029}, {"pid": 660, "tid": 660, "ts": 39185733817, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 189, "tdur": 189, "tts": 227046}, {"pid": 660, "tid": 660, "ts": 39185734020, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 227249}, {"pid": 660, "tid": 660, "ts": 39185750305, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 227702}, {"pid": 660, "tid": 660, "ts": 39185750580, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 227977}, {"pid": 660, "tid": 660, "ts": 39185750595, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 185, "tdur": 185, "tts": 227991}, {"pid": 660, "tid": 660, "ts": 39185750794, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 74, "tts": 228190}, {"pid": 660, "tid": 660, "ts": 39185766933, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 228678}, {"pid": 660, "tid": 660, "ts": 39185767346, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 229090}, {"pid": 660, "tid": 660, "ts": 39185767363, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 405, "tdur": 406, "tts": 229106}, {"pid": 660, "tid": 660, "ts": 39185767787, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 65, "tdur": 65, "tts": 229530}, {"pid": 660, "tid": 660, "ts": 39185783068, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 230027}, {"pid": 660, "tid": 660, "ts": 39185783380, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 230339}, {"pid": 660, "tid": 660, "ts": 39185783397, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 195, "tdur": 196, "tts": 230354}, {"pid": 660, "tid": 660, "ts": 39185783627, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 63, "tts": 230584}, {"pid": 660, "tid": 660, "ts": 39185800265, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 231356}, {"pid": 660, "tid": 660, "ts": 39185800647, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 231738}, {"pid": 660, "tid": 660, "ts": 39185800710, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 405, "tdur": 405, "tts": 231800}, {"pid": 660, "tid": 660, "ts": 39185801133, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 76, "tts": 232222}, {"pid": 660, "tid": 660, "ts": 39185816741, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 232687}, {"pid": 660, "tid": 660, "ts": 39185817105, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 233050}, {"pid": 660, "tid": 660, "ts": 39185817121, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 235, "tdur": 236, "tts": 233066}, {"pid": 660, "tid": 660, "ts": 39185817372, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 72, "tdur": 71, "tts": 233317}, {"pid": 660, "tid": 660, "ts": 39185833822, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 233804}, {"pid": 660, "tid": 660, "ts": 39185834085, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 234067}, {"pid": 660, "tid": 660, "ts": 39185834099, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 209, "tts": 234080}, {"pid": 660, "tid": 660, "ts": 39185834322, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 234303}, {"pid": 660, "tid": 660, "ts": 39185849796, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 234747}, {"pid": 660, "tid": 660, "ts": 39185850128, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 235079}, {"pid": 660, "tid": 660, "ts": 39185850145, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 244, "tdur": 244, "tts": 235095}, {"pid": 660, "tid": 660, "ts": 39185850402, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 77, "tdur": 77, "tts": 235352}, {"pid": 660, "tid": 660, "ts": 39185866772, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 235831}, {"pid": 660, "tid": 660, "ts": 39185867132, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 236190}, {"pid": 660, "tid": 660, "ts": 39185867148, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 198, "tdur": 199, "tts": 236205}, {"pid": 660, "tid": 660, "ts": 39185867362, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 236419}, {"pid": 660, "tid": 660, "ts": 39185883431, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 237034}, {"pid": 660, "tid": 660, "ts": 39185883738, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 237340}, {"pid": 660, "tid": 660, "ts": 39185883752, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 196, "tdur": 196, "tts": 237353}, {"pid": 660, "tid": 660, "ts": 39185883962, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 125, "tdur": 124, "tts": 237564}, {"pid": 660, "tid": 660, "ts": 39185900455, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 238126}, {"pid": 660, "tid": 660, "ts": 39185900964, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 238635}, {"pid": 660, "tid": 660, "ts": 39185900980, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 199, "tdur": 200, "tts": 238650}, {"pid": 660, "tid": 660, "ts": 39185901193, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 238863}, {"pid": 660, "tid": 660, "ts": 39185916919, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 239452}, {"pid": 660, "tid": 660, "ts": 39185917278, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 239811}, {"pid": 660, "tid": 660, "ts": 39185917294, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 296, "tdur": 297, "tts": 239826}, {"pid": 660, "tid": 660, "ts": 39185917607, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 69, "tdur": 68, "tts": 240139}, {"pid": 660, "tid": 660, "ts": 39185934017, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 240831}, {"pid": 660, "tid": 660, "ts": 39185934290, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 241102}, {"pid": 660, "tid": 660, "ts": 39185934304, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 201, "tdur": 201, "tts": 241116}, {"pid": 660, "tid": 660, "ts": 39185934519, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 71, "tdur": 71, "tts": 241330}, {"pid": 660, "tid": 660, "ts": 39185950089, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 241815}, {"pid": 660, "tid": 660, "ts": 39185950365, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 242091}, {"pid": 660, "tid": 660, "ts": 39185950379, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 197, "tdur": 198, "tts": 242104}, {"pid": 660, "tid": 660, "ts": 39185950599, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 242324}, {"pid": 660, "tid": 660, "ts": 39185967273, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 242974}, {"pid": 660, "tid": 660, "ts": 39185967558, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 243257}, {"pid": 660, "tid": 660, "ts": 39185967573, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 220, "tdur": 220, "tts": 243272}, {"pid": 660, "tid": 660, "ts": 39185967807, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 59, "tts": 243506}, {"pid": 660, "tid": 660, "ts": 39185984972, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 243940}, {"pid": 660, "tid": 660, "ts": 39185985246, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 244213}, {"pid": 660, "tid": 660, "ts": 39185985260, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 304, "tdur": 304, "tts": 244227}, {"pid": 660, "tid": 660, "ts": 39185985579, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 118, "tdur": 117, "tts": 244547}, {"pid": 660, "tid": 660, "ts": 39186001053, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 245073}, {"pid": 660, "tid": 660, "ts": 39186001435, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 245455}, {"pid": 660, "tid": 660, "ts": 39186001455, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 256, "tdur": 256, "tts": 245474}, {"pid": 660, "tid": 660, "ts": 39186001725, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 245744}, {"pid": 660, "tid": 660, "ts": 39186017390, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 246325}, {"pid": 660, "tid": 660, "ts": 39186017665, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 246598}, {"pid": 660, "tid": 660, "ts": 39186017680, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 463, "tdur": 464, "tts": 246613}, {"pid": 660, "tid": 660, "ts": 39186018161, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 64, "tdur": 64, "tts": 247094}, {"pid": 660, "tid": 660, "ts": 39186033546, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 247572}, {"pid": 660, "tid": 660, "ts": 39186033808, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 247833}, {"pid": 660, "tid": 660, "ts": 39186033822, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 255, "tdur": 256, "tts": 247846}, {"pid": 660, "tid": 660, "ts": 39186034093, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 76, "tdur": 75, "tts": 248118}, {"pid": 660, "tid": 660, "ts": 39186050633, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 248714}, {"pid": 660, "tid": 660, "ts": 39186050896, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 248976}, {"pid": 660, "tid": 660, "ts": 39186050910, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 204, "tdur": 204, "tts": 248990}, {"pid": 660, "tid": 660, "ts": 39186051128, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 249208}, {"pid": 660, "tid": 660, "ts": 39186066670, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 249732}, {"pid": 660, "tid": 660, "ts": 39186067177, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 250237}, {"pid": 660, "tid": 660, "ts": 39186067194, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 203, "tdur": 203, "tts": 250254}, {"pid": 660, "tid": 660, "ts": 39186067412, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 418, "tdur": 420, "tts": 250471}, {"pid": 660, "tid": 660, "ts": 39186083432, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 251341}, {"pid": 660, "tid": 660, "ts": 39186083716, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 251625}, {"pid": 660, "tid": 660, "ts": 39186083730, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 231, "tdur": 231, "tts": 251638}, {"pid": 660, "tid": 660, "ts": 39186083977, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 65, "tdur": 64, "tts": 251885}, {"pid": 660, "tid": 660, "ts": 39186100354, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 252460}, {"pid": 660, "tid": 660, "ts": 39186100613, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 252719}, {"pid": 660, "tid": 660, "ts": 39186100627, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 276, "tdur": 276, "tts": 252732}, {"pid": 660, "tid": 660, "ts": 39186100921, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 253026}, {"pid": 660, "tid": 660, "ts": 39186123189, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 253581}, {"pid": 660, "tid": 660, "ts": 39186123514, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 253905}, {"pid": 660, "tid": 660, "ts": 39186123532, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 219, "tdur": 218, "tts": 253923}, {"pid": 660, "tid": 660, "ts": 39186123767, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 62, "tts": 254157}, {"pid": 660, "tid": 660, "ts": 39186134094, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 254830}, {"pid": 660, "tid": 660, "ts": 39186134371, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 255106}, {"pid": 660, "tid": 660, "ts": 39186134386, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 280, "tdur": 281, "tts": 255120}, {"pid": 660, "tid": 660, "ts": 39186134681, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 255415}, {"pid": 660, "tid": 660, "ts": 39186149626, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 255911}, {"pid": 660, "tid": 660, "ts": 39186150253, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 256538}, {"pid": 660, "tid": 660, "ts": 39186150269, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 347, "tdur": 347, "tts": 256554}, {"pid": 660, "tid": 660, "ts": 39186150668, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 87, "tdur": 86, "tts": 256953}, {"pid": 660, "tid": 660, "ts": 39186167467, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 258053}, {"pid": 660, "tid": 660, "ts": 39186168079, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 258665}, {"pid": 660, "tid": 660, "ts": 39186168095, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 289, "tdur": 289, "tts": 258680}, {"pid": 660, "tid": 660, "ts": 39186168399, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 75, "tts": 258984}, {"pid": 660, "tid": 660, "ts": 39186183325, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 259441}, {"pid": 660, "tid": 660, "ts": 39186183581, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 259696}, {"pid": 660, "tid": 660, "ts": 39186183611, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 197, "tdur": 197, "tts": 259726}, {"pid": 660, "tid": 660, "ts": 39186183823, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 58, "tdur": 59, "tts": 259937}, {"pid": 660, "tid": 660, "ts": 39186199821, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 260414}, {"pid": 660, "tid": 660, "ts": 39186200454, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 261047}, {"pid": 660, "tid": 660, "ts": 39186200471, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 204, "tdur": 204, "tts": 261063}, {"pid": 660, "tid": 660, "ts": 39186200689, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 141, "tdur": 141, "tts": 261281}, {"pid": 660, "tid": 660, "ts": 39186216476, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 261918}, {"pid": 660, "tid": 660, "ts": 39186216866, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 262308}, {"pid": 660, "tid": 660, "ts": 39186216885, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 215, "tdur": 216, "tts": 262325}, {"pid": 660, "tid": 660, "ts": 39186217118, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 137, "tdur": 136, "tts": 262559}, {"pid": 660, "tid": 660, "ts": 39186234020, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 263197}, {"pid": 660, "tid": 660, "ts": 39186234314, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 263489}, {"pid": 660, "tid": 660, "ts": 39186234327, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 282, "tdur": 283, "tts": 263502}, {"pid": 660, "tid": 660, "ts": 39186234627, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 77, "tdur": 78, "tts": 263802}, {"pid": 660, "tid": 660, "ts": 39186250148, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 264579}, {"pid": 660, "tid": 660, "ts": 39186250705, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 265135}, {"pid": 660, "tid": 660, "ts": 39186250722, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 380, "tdur": 380, "tts": 265151}, {"pid": 660, "tid": 660, "ts": 39186251117, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 87, "tdur": 87, "tts": 265546}, {"pid": 660, "tid": 660, "ts": 39186267212, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 266220}, {"pid": 660, "tid": 660, "ts": 39186267659, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 266666}, {"pid": 660, "tid": 660, "ts": 39186267675, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 258, "tdur": 259, "tts": 266681}, {"pid": 660, "tid": 660, "ts": 39186267949, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 246, "tdur": 247, "tts": 266955}, {"pid": 660, "tid": 660, "ts": 39186283844, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 267701}, {"pid": 660, "tid": 660, "ts": 39186284107, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 267964}, {"pid": 660, "tid": 660, "ts": 39186284120, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 198, "tdur": 199, "tts": 267976}, {"pid": 660, "tid": 660, "ts": 39186284333, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 268190}, {"pid": 660, "tid": 660, "ts": 39186300174, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 268762}, {"pid": 660, "tid": 660, "ts": 39186300628, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 269215}, {"pid": 660, "tid": 660, "ts": 39186300644, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 220, "tdur": 220, "tts": 269230}, {"pid": 660, "tid": 660, "ts": 39186300878, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 269464}, {"pid": 660, "tid": 660, "ts": 39186317232, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 269931}, {"pid": 660, "tid": 660, "ts": 39186317680, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 270379}, {"pid": 660, "tid": 660, "ts": 39186317770, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 208, "tdur": 208, "tts": 270468}, {"pid": 660, "tid": 660, "ts": 39186317993, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 72, "tdur": 72, "tts": 270690}, {"pid": 660, "tid": 660, "ts": 39186333135, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 271305}, {"pid": 660, "tid": 660, "ts": 39186333413, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 271582}, {"pid": 660, "tid": 660, "ts": 39186333428, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 207, "tts": 271596}, {"pid": 660, "tid": 660, "ts": 39186333648, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 76, "tdur": 75, "tts": 271817}, {"pid": 660, "tid": 660, "ts": 39186350494, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 272350}, {"pid": 660, "tid": 660, "ts": 39186350822, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 272678}, {"pid": 660, "tid": 660, "ts": 39186350839, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 203, "tdur": 202, "tts": 272695}, {"pid": 660, "tid": 660, "ts": 39186351058, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 58, "tdur": 58, "tts": 272914}, {"pid": 660, "tid": 660, "ts": 39186366377, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 273405}, {"pid": 660, "tid": 660, "ts": 39186366742, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 273770}, {"pid": 660, "tid": 660, "ts": 39186366759, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 281, "tdur": 282, "tts": 273785}, {"pid": 660, "tid": 660, "ts": 39186367083, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 89, "tdur": 90, "tts": 274109}, {"pid": 660, "tid": 660, "ts": 39186383421, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 274910}, {"pid": 660, "tid": 660, "ts": 39186383708, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 275197}, {"pid": 660, "tid": 660, "ts": 39186383723, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 200, "tdur": 199, "tts": 275211}, {"pid": 660, "tid": 660, "ts": 39186383937, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 62, "tts": 275424}, {"pid": 660, "tid": 660, "ts": 39186405612, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 277200}, {"pid": 660, "tid": 660, "ts": 39186405887, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 277473}, {"pid": 660, "tid": 660, "ts": 39186405902, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 983, "tdur": 983, "tts": 277488}, {"pid": 660, "tid": 660, "ts": 39186406902, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 64, "tdur": 64, "tts": 278488}, {"pid": 660, "tid": 660, "ts": 39186416362, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 279028}, {"pid": 660, "tid": 660, "ts": 39186416627, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 279292}, {"pid": 660, "tid": 660, "ts": 39186416641, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 227, "tdur": 227, "tts": 279306}, {"pid": 660, "tid": 660, "ts": 39186416883, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 279548}, {"pid": 660, "tid": 660, "ts": 39186433214, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 280074}, {"pid": 660, "tid": 660, "ts": 39186433480, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 280339}, {"pid": 660, "tid": 660, "ts": 39186433494, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 214, "tdur": 215, "tts": 280352}, {"pid": 660, "tid": 660, "ts": 39186433724, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 62, "tts": 280583}, {"pid": 660, "tid": 660, "ts": 39186449988, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 281050}, {"pid": 660, "tid": 660, "ts": 39186450253, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 281314}, {"pid": 660, "tid": 660, "ts": 39186450267, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 391, "tdur": 392, "tts": 281328}, {"pid": 660, "tid": 660, "ts": 39186450674, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 75, "tts": 281735}, {"pid": 660, "tid": 660, "ts": 39186467196, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 282261}, {"pid": 660, "tid": 660, "ts": 39186467461, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 282525}, {"pid": 660, "tid": 660, "ts": 39186467475, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 295, "tdur": 296, "tts": 282539}, {"pid": 660, "tid": 660, "ts": 39186467789, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 67, "tdur": 67, "tts": 282853}, {"pid": 660, "tid": 660, "ts": 39186483258, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 283431}, {"pid": 660, "tid": 660, "ts": 39186483547, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 283720}, {"pid": 660, "tid": 660, "ts": 39186483563, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 224, "tdur": 224, "tts": 283735}, {"pid": 660, "tid": 660, "ts": 39186483802, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 59, "tts": 283974}, {"pid": 660, "tid": 660, "ts": 39186516452, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 284507}, {"pid": 660, "tid": 660, "ts": 39186516727, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 284781}, {"pid": 660, "tid": 660, "ts": 39186516740, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 236, "tdur": 236, "tts": 284794}, {"pid": 660, "tid": 660, "ts": 39186516990, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 64, "tts": 285043}, {"pid": 660, "tid": 660, "ts": 39186533618, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 285512}, {"pid": 660, "tid": 660, "ts": 39186533900, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 285793}, {"pid": 660, "tid": 660, "ts": 39186533973, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 301, "tdur": 301, "tts": 285866}, {"pid": 660, "tid": 660, "ts": 39186534289, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 62, "tts": 286182}, {"pid": 660, "tid": 660, "ts": 39186550459, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 286647}, {"pid": 660, "tid": 660, "ts": 39186550723, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 286910}, {"pid": 660, "tid": 660, "ts": 39186550737, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 205, "tdur": 204, "tts": 286923}, {"pid": 660, "tid": 660, "ts": 39186550956, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 287142}, {"pid": 660, "tid": 660, "ts": 39186567052, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 287599}, {"pid": 660, "tid": 660, "ts": 39186567310, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 287856}, {"pid": 660, "tid": 660, "ts": 39186567324, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 196, "tdur": 197, "tts": 287869}, {"pid": 660, "tid": 660, "ts": 39186567535, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 76, "tdur": 76, "tts": 288081}, {"pid": 660, "tid": 660, "ts": 39186583308, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 288644}, {"pid": 660, "tid": 660, "ts": 39186583893, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 289229}, {"pid": 660, "tid": 660, "ts": 39186583911, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 200, "tdur": 200, "tts": 289246}, {"pid": 660, "tid": 660, "ts": 39186584124, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 61, "tts": 289459}, {"pid": 660, "tid": 660, "ts": 39186600331, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 289916}, {"pid": 660, "tid": 660, "ts": 39186600595, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 290179}, {"pid": 660, "tid": 660, "ts": 39186600608, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 270, "tdur": 271, "tts": 290191}, {"pid": 660, "tid": 660, "ts": 39186600894, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 290477}, {"pid": 660, "tid": 660, "ts": 39186616955, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 290938}, {"pid": 660, "tid": 660, "ts": 39186617221, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 291202}, {"pid": 660, "tid": 660, "ts": 39186617234, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 209, "tdur": 209, "tts": 291215}, {"pid": 660, "tid": 660, "ts": 39186617458, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 86, "tdur": 86, "tts": 291439}, {"pid": 660, "tid": 660, "ts": 39186633651, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 291929}, {"pid": 660, "tid": 660, "ts": 39186633920, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 292197}, {"pid": 660, "tid": 660, "ts": 39186633934, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 314, "tdur": 314, "tts": 292210}, {"pid": 660, "tid": 660, "ts": 39186634265, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 292542}, {"pid": 660, "tid": 660, "ts": 39186650307, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 293003}, {"pid": 660, "tid": 660, "ts": 39186650573, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 293268}, {"pid": 660, "tid": 660, "ts": 39186650587, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 208, "tdur": 209, "tts": 293281}, {"pid": 660, "tid": 660, "ts": 39186650811, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 60, "tts": 293505}, {"pid": 660, "tid": 660, "ts": 39186666493, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 294038}, {"pid": 660, "tid": 660, "ts": 39186666754, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 294298}, {"pid": 660, "tid": 660, "ts": 39186666767, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 204, "tdur": 205, "tts": 294310}, {"pid": 660, "tid": 660, "ts": 39186666986, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 294530}, {"pid": 660, "tid": 660, "ts": 39186683740, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 295045}, {"pid": 660, "tid": 660, "ts": 39186684000, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 295304}, {"pid": 660, "tid": 660, "ts": 39186684013, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 197, "tdur": 197, "tts": 295317}, {"pid": 660, "tid": 660, "ts": 39186684224, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 295528}, {"pid": 660, "tid": 660, "ts": 39186700576, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 296155}, {"pid": 660, "tid": 660, "ts": 39186701078, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 296658}, {"pid": 660, "tid": 660, "ts": 39186701096, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 203, "tdur": 202, "tts": 296675}, {"pid": 660, "tid": 660, "ts": 39186701313, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 296891}, {"pid": 660, "tid": 660, "ts": 39186716562, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 297427}, {"pid": 660, "tid": 660, "ts": 39186716915, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 297780}, {"pid": 660, "tid": 660, "ts": 39186716930, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 213, "tdur": 213, "tts": 297795}, {"pid": 660, "tid": 660, "ts": 39186717157, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 60, "tts": 298022}, {"pid": 660, "tid": 660, "ts": 39186733798, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 298481}, {"pid": 660, "tid": 660, "ts": 39186734071, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 298753}, {"pid": 660, "tid": 660, "ts": 39186734085, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 202, "tdur": 203, "tts": 298766}, {"pid": 660, "tid": 660, "ts": 39186734301, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 62, "tts": 298983}, {"pid": 660, "tid": 660, "ts": 39186750513, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 299525}, {"pid": 660, "tid": 660, "ts": 39186750783, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 299795}, {"pid": 660, "tid": 660, "ts": 39186750799, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 262, "tdur": 263, "tts": 299809}, {"pid": 660, "tid": 660, "ts": 39186751077, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 72, "tdur": 71, "tts": 300088}, {"pid": 660, "tid": 660, "ts": 39186766256, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 300583}, {"pid": 660, "tid": 660, "ts": 39186766520, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 300847}, {"pid": 660, "tid": 660, "ts": 39186766534, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 195, "tdur": 196, "tts": 300860}, {"pid": 660, "tid": 660, "ts": 39186766744, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 57, "tdur": 57, "tts": 301070}, {"pid": 660, "tid": 660, "ts": 39186783817, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 301521}, {"pid": 660, "tid": 660, "ts": 39186784253, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 301956}, {"pid": 660, "tid": 660, "ts": 39186784271, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 297, "tdur": 298, "tts": 301973}, {"pid": 660, "tid": 660, "ts": 39186784586, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 1507, "tdur": 1507, "tts": 302288}, {"pid": 660, "tid": 660, "ts": 39186816773, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 306043}, {"pid": 660, "tid": 660, "ts": 39186817056, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 306325}, {"pid": 660, "tid": 660, "ts": 39186817070, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 201, "tdur": 202, "tts": 306338}, {"pid": 660, "tid": 660, "ts": 39186817285, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 116, "tdur": 116, "tts": 306554}, {"pid": 660, "tid": 660, "ts": 39186833270, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 307083}, {"pid": 660, "tid": 660, "ts": 39186833549, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 307362}, {"pid": 660, "tid": 660, "ts": 39186833563, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 210, "tdur": 210, "tts": 307376}, {"pid": 660, "tid": 660, "ts": 39186833788, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 120, "tdur": 120, "tts": 307601}, {"pid": 660, "tid": 660, "ts": 39186849708, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 308146}, {"pid": 660, "tid": 660, "ts": 39186849972, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 308410}, {"pid": 660, "tid": 660, "ts": 39186849986, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 198, "tdur": 199, "tts": 308423}, {"pid": 660, "tid": 660, "ts": 39186850199, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 59, "tts": 308637}, {"pid": 660, "tid": 660, "ts": 39186866786, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 309095}, {"pid": 660, "tid": 660, "ts": 39186867303, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 309612}, {"pid": 660, "tid": 660, "ts": 39186867321, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 201, "tdur": 202, "tts": 309628}, {"pid": 660, "tid": 660, "ts": 39186867536, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 84, "tdur": 84, "tts": 309844}, {"pid": 660, "tid": 660, "ts": 39186883583, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 310404}, {"pid": 660, "tid": 660, "ts": 39186883865, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 310684}, {"pid": 660, "tid": 660, "ts": 39186883879, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 210, "tdur": 210, "tts": 310698}, {"pid": 660, "tid": 660, "ts": 39186884104, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 310923}, {"pid": 660, "tid": 660, "ts": 39186900409, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 311516}, {"pid": 660, "tid": 660, "ts": 39186900673, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 311779}, {"pid": 660, "tid": 660, "ts": 39186900687, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 231, "tdur": 231, "tts": 311793}, {"pid": 660, "tid": 660, "ts": 39186900934, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 75, "tts": 312040}, {"pid": 660, "tid": 660, "ts": 39186916745, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 312545}, {"pid": 660, "tid": 660, "ts": 39186917112, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 312893}, {"pid": 660, "tid": 660, "ts": 39186917129, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 201, "tdur": 202, "tts": 312909}, {"pid": 660, "tid": 660, "ts": 39186917345, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 75, "tdur": 76, "tts": 313125}, {"pid": 660, "tid": 660, "ts": 39186933143, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 313612}, {"pid": 660, "tid": 660, "ts": 39186933405, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 313873}, {"pid": 660, "tid": 660, "ts": 39186933419, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 214, "tdur": 215, "tts": 313886}, {"pid": 660, "tid": 660, "ts": 39186933650, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 59, "tts": 314118}, {"pid": 660, "tid": 660, "ts": 39186952899, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 314757}, {"pid": 660, "tid": 660, "ts": 39186953690, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 315547}, {"pid": 660, "tid": 660, "ts": 39186953708, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 1702, "tdur": 1703, "tts": 315564}, {"pid": 660, "tid": 660, "ts": 39186955433, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 74, "tdur": 74, "tts": 317289}, {"pid": 660, "tid": 660, "ts": 39186967753, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 318048}, {"pid": 660, "tid": 660, "ts": 39186968402, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 318697}, {"pid": 660, "tid": 660, "ts": 39186968424, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 482, "tdur": 483, "tts": 318717}, {"pid": 660, "tid": 660, "ts": 39186968923, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 319216}, {"pid": 660, "tid": 660, "ts": 39186982995, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 320183}, {"pid": 660, "tid": 660, "ts": 39186983254, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 320441}, {"pid": 660, "tid": 660, "ts": 39186983268, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 191, "tdur": 192, "tts": 320454}, {"pid": 660, "tid": 660, "ts": 39186983474, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 207, "tdur": 208, "tts": 320660}, {"pid": 660, "tid": 660, "ts": 39187000204, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 321303}, {"pid": 660, "tid": 660, "ts": 39187000466, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 321564}, {"pid": 660, "tid": 660, "ts": 39187000479, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 228, "tdur": 228, "tts": 321577}, {"pid": 660, "tid": 660, "ts": 39187000722, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 87, "tdur": 87, "tts": 321820}, {"pid": 660, "tid": 660, "ts": 39187017784, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 322333}, {"pid": 660, "tid": 660, "ts": 39187018461, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 323010}, {"pid": 660, "tid": 660, "ts": 39187018480, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 230, "tdur": 230, "tts": 323028}, {"pid": 660, "tid": 660, "ts": 39187018725, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 77, "tdur": 76, "tts": 323274}, {"pid": 660, "tid": 660, "ts": 39187033002, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 323758}, {"pid": 660, "tid": 660, "ts": 39187033263, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 324018}, {"pid": 660, "tid": 660, "ts": 39187033276, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 194, "tdur": 194, "tts": 324031}, {"pid": 660, "tid": 660, "ts": 39187033484, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 324239}, {"pid": 660, "tid": 660, "ts": 39187050442, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 325778}, {"pid": 660, "tid": 660, "ts": 39187050721, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 326057}, {"pid": 660, "tid": 660, "ts": 39187050735, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 260, "tdur": 260, "tts": 326070}, {"pid": 660, "tid": 660, "ts": 39187051011, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 74, "tdur": 73, "tts": 326346}, {"pid": 660, "tid": 660, "ts": 39187066966, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 326900}, {"pid": 660, "tid": 660, "ts": 39187067309, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 327244}, {"pid": 660, "tid": 660, "ts": 39187067326, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 682, "tdur": 685, "tts": 327259}, {"pid": 660, "tid": 660, "ts": 39187068030, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 67, "tdur": 67, "tts": 327963}, {"pid": 660, "tid": 660, "ts": 39187083202, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 328444}, {"pid": 660, "tid": 660, "ts": 39187083653, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 328895}, {"pid": 660, "tid": 660, "ts": 39187083674, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 212, "tdur": 213, "tts": 328914}, {"pid": 660, "tid": 660, "ts": 39187083903, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 82, "tdur": 81, "tts": 329144}, {"pid": 660, "tid": 660, "ts": 39187100061, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 329648}, {"pid": 660, "tid": 660, "ts": 39187100327, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 329912}, {"pid": 660, "tid": 660, "ts": 39187100341, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 196, "tdur": 197, "tts": 329925}, {"pid": 660, "tid": 660, "ts": 39187100550, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 330135}, {"pid": 660, "tid": 660, "ts": 39187116840, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 330680}, {"pid": 660, "tid": 660, "ts": 39187117122, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 330962}, {"pid": 660, "tid": 660, "ts": 39187117137, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 212, "tdur": 212, "tts": 330977}, {"pid": 660, "tid": 660, "ts": 39187117363, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 64, "tdur": 64, "tts": 331202}, {"pid": 660, "tid": 660, "ts": 39187134034, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 331795}, {"pid": 660, "tid": 660, "ts": 39187134296, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 332056}, {"pid": 660, "tid": 660, "ts": 39187134309, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 207, "tts": 332069}, {"pid": 660, "tid": 660, "ts": 39187134529, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 332289}, {"pid": 660, "tid": 660, "ts": 39187150057, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 332755}, {"pid": 660, "tid": 660, "ts": 39187150314, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 333012}, {"pid": 660, "tid": 660, "ts": 39187150329, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 207, "tts": 333025}, {"pid": 660, "tid": 660, "ts": 39187150550, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 333247}, {"pid": 660, "tid": 660, "ts": 39187166321, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 333704}, {"pid": 660, "tid": 660, "ts": 39187166588, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 333970}, {"pid": 660, "tid": 660, "ts": 39187166602, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 223, "tdur": 224, "tts": 333983}, {"pid": 660, "tid": 660, "ts": 39187166840, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 90, "tdur": 90, "tts": 334221}, {"pid": 660, "tid": 660, "ts": 39187183718, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 334721}, {"pid": 660, "tid": 660, "ts": 39187183982, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 334984}, {"pid": 660, "tid": 660, "ts": 39187183997, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 202, "tdur": 201, "tts": 334999}, {"pid": 660, "tid": 660, "ts": 39187184213, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 62, "tts": 335214}, {"pid": 660, "tid": 660, "ts": 39187199840, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 335668}, {"pid": 660, "tid": 660, "ts": 39187200098, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 335925}, {"pid": 660, "tid": 660, "ts": 39187200112, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 201, "tdur": 201, "tts": 335939}, {"pid": 660, "tid": 660, "ts": 39187200327, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 72, "tdur": 72, "tts": 336154}, {"pid": 660, "tid": 660, "ts": 39187217024, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 336668}, {"pid": 660, "tid": 660, "ts": 39187217286, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 336930}, {"pid": 660, "tid": 660, "ts": 39187217300, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 202, "tdur": 203, "tts": 336943}, {"pid": 660, "tid": 660, "ts": 39187217516, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 337160}, {"pid": 660, "tid": 660, "ts": 39187233178, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 337618}, {"pid": 660, "tid": 660, "ts": 39187233453, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 337893}, {"pid": 660, "tid": 660, "ts": 39187233467, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 413, "tdur": 415, "tts": 337906}, {"pid": 660, "tid": 660, "ts": 39187233900, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 61, "tts": 338340}, {"pid": 660, "tid": 660, "ts": 39187250826, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 338792}, {"pid": 660, "tid": 660, "ts": 39187251080, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 339046}, {"pid": 660, "tid": 660, "ts": 39187251095, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 222, "tdur": 224, "tts": 339059}, {"pid": 660, "tid": 660, "ts": 39187251332, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 339297}, {"pid": 660, "tid": 660, "ts": 39187268013, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 339788}, {"pid": 660, "tid": 660, "ts": 39187268283, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 340057}, {"pid": 660, "tid": 660, "ts": 39187268298, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 206, "tts": 340072}, {"pid": 660, "tid": 660, "ts": 39187268518, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 340292}, {"pid": 660, "tid": 660, "ts": 39187283168, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 340904}, {"pid": 660, "tid": 660, "ts": 39187283424, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 341160}, {"pid": 660, "tid": 660, "ts": 39187283437, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 209, "tdur": 209, "tts": 341173}, {"pid": 660, "tid": 660, "ts": 39187283661, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 341396}, {"pid": 660, "tid": 660, "ts": 39187301223, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 341932}, {"pid": 660, "tid": 660, "ts": 39187301487, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 342195}, {"pid": 660, "tid": 660, "ts": 39187301501, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 205, "tdur": 206, "tts": 342208}, {"pid": 660, "tid": 660, "ts": 39187301721, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 342429}, {"pid": 660, "tid": 660, "ts": 39187316438, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 342962}, {"pid": 660, "tid": 660, "ts": 39187316810, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 343273}, {"pid": 660, "tid": 660, "ts": 39187316825, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 308, "tdur": 309, "tts": 343287}, {"pid": 660, "tid": 660, "ts": 39187317148, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 343611}, {"pid": 660, "tid": 660, "ts": 39187333144, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 344471}, {"pid": 660, "tid": 660, "ts": 39187333405, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 344731}, {"pid": 660, "tid": 660, "ts": 39187333419, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 235, "tdur": 235, "tts": 344745}, {"pid": 660, "tid": 660, "ts": 39187333668, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 70, "tdur": 69, "tts": 344994}, {"pid": 660, "tid": 660, "ts": 39187350513, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 345456}, {"pid": 660, "tid": 660, "ts": 39187350778, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 345720}, {"pid": 660, "tid": 660, "ts": 39187350793, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 349, "tdur": 350, "tts": 345735}, {"pid": 660, "tid": 660, "ts": 39187351174, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 313, "tdur": 313, "tts": 346116}, {"pid": 660, "tid": 660, "ts": 39187367139, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 346861}, {"pid": 660, "tid": 660, "ts": 39187367472, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 347194}, {"pid": 660, "tid": 660, "ts": 39187367490, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 211, "tdur": 211, "tts": 347211}, {"pid": 660, "tid": 660, "ts": 39187367716, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 59, "tts": 347437}, {"pid": 660, "tid": 660, "ts": 39187385891, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 348716}, {"pid": 660, "tid": 660, "ts": 39187386177, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 349002}, {"pid": 660, "tid": 660, "ts": 39187386191, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 793, "tdur": 795, "tts": 349015}, {"pid": 660, "tid": 660, "ts": 39187387004, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 349829}, {"pid": 660, "tid": 660, "ts": 39187399894, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 350332}, {"pid": 660, "tid": 660, "ts": 39187400237, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 350675}, {"pid": 660, "tid": 660, "ts": 39187400254, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 199, "tdur": 200, "tts": 350690}, {"pid": 660, "tid": 660, "ts": 39187400468, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 58, "tts": 350905}, {"pid": 660, "tid": 660, "ts": 39187417156, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 351455}, {"pid": 660, "tid": 660, "ts": 39187417413, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 351712}, {"pid": 660, "tid": 660, "ts": 39187417426, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 320, "tdur": 321, "tts": 351724}, {"pid": 660, "tid": 660, "ts": 39187417764, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 352062}, {"pid": 660, "tid": 660, "ts": 39187435754, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 352639}, {"pid": 660, "tid": 660, "ts": 39187436196, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 353081}, {"pid": 660, "tid": 660, "ts": 39187436212, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 278, "tdur": 278, "tts": 353096}, {"pid": 660, "tid": 660, "ts": 39187436505, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 77, "tdur": 77, "tts": 353389}, {"pid": 660, "tid": 660, "ts": 39187450444, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 353858}, {"pid": 660, "tid": 660, "ts": 39187450694, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 354107}, {"pid": 660, "tid": 660, "ts": 39187450708, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 196, "tdur": 197, "tts": 354120}, {"pid": 660, "tid": 660, "ts": 39187450953, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 62, "tts": 354366}, {"pid": 660, "tid": 660, "ts": 39187467256, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 355594}, {"pid": 660, "tid": 660, "ts": 39187467520, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 355857}, {"pid": 660, "tid": 660, "ts": 39187467534, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 274, "tdur": 274, "tts": 355870}, {"pid": 660, "tid": 660, "ts": 39187467824, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 356160}, {"pid": 660, "tid": 660, "ts": 39187484153, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 356955}, {"pid": 660, "tid": 660, "ts": 39187484408, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 357208}, {"pid": 660, "tid": 660, "ts": 39187484420, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 972, "tdur": 967, "tts": 357220}, {"pid": 660, "tid": 660, "ts": 39187485410, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 358204}, {"pid": 660, "tid": 660, "ts": 39187499715, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 358699}, {"pid": 660, "tid": 660, "ts": 39187499978, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 358961}, {"pid": 660, "tid": 660, "ts": 39187499993, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 205, "tdur": 206, "tts": 358975}, {"pid": 660, "tid": 660, "ts": 39187500213, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 62, "tts": 359196}, {"pid": 660, "tid": 660, "ts": 39187516787, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 359697}, {"pid": 660, "tid": 660, "ts": 39187517050, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 359960}, {"pid": 660, "tid": 660, "ts": 39187517064, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 186, "tdur": 186, "tts": 359973}, {"pid": 660, "tid": 660, "ts": 39187517263, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 122, "tdur": 123, "tts": 360172}, {"pid": 660, "tid": 660, "ts": 39187536038, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 360692}, {"pid": 660, "tid": 660, "ts": 39187536326, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 360979}, {"pid": 660, "tid": 660, "ts": 39187536341, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 188, "tdur": 187, "tts": 360994}, {"pid": 660, "tid": 660, "ts": 39187536543, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 361195}, {"pid": 660, "tid": 660, "ts": 39187549592, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 361655}, {"pid": 660, "tid": 660, "ts": 39187549852, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 361914}, {"pid": 660, "tid": 660, "ts": 39187549867, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 205, "tdur": 205, "tts": 361928}, {"pid": 660, "tid": 660, "ts": 39187550090, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 60, "tdur": 60, "tts": 362151}, {"pid": 660, "tid": 660, "ts": 39187567027, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 362661}, {"pid": 660, "tid": 660, "ts": 39187567293, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 362926}, {"pid": 660, "tid": 660, "ts": 39187567307, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 200, "tdur": 199, "tts": 362940}, {"pid": 660, "tid": 660, "ts": 39187567521, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 61, "tts": 363154}, {"pid": 660, "tid": 660, "ts": 39187583809, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 363649}, {"pid": 660, "tid": 660, "ts": 39187587908, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 367743}, {"pid": 660, "tid": 660, "ts": 39187587933, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 223, "tdur": 223, "tts": 367765}, {"pid": 660, "tid": 660, "ts": 39187588169, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 368001}, {"pid": 660, "tid": 660, "ts": 39187600282, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 368469}, {"pid": 660, "tid": 660, "ts": 39187600537, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 368723}, {"pid": 660, "tid": 660, "ts": 39187600550, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 192, "tdur": 193, "tts": 368736}, {"pid": 660, "tid": 660, "ts": 39187600785, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 368971}, {"pid": 660, "tid": 660, "ts": 39187616932, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 369429}, {"pid": 660, "tid": 660, "ts": 39187617201, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 369698}, {"pid": 660, "tid": 660, "ts": 39187617215, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 250, "tdur": 250, "tts": 369711}, {"pid": 660, "tid": 660, "ts": 39187617481, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 369977}, {"pid": 660, "tid": 660, "ts": 39187633770, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 370450}, {"pid": 660, "tid": 660, "ts": 39187634028, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 370708}, {"pid": 660, "tid": 660, "ts": 39187634042, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 205, "tdur": 205, "tts": 370721}, {"pid": 660, "tid": 660, "ts": 39187634261, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 370940}, {"pid": 660, "tid": 660, "ts": 39187653047, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 371451}, {"pid": 660, "tid": 660, "ts": 39187653311, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 371715}, {"pid": 660, "tid": 660, "ts": 39187653325, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 315, "tdur": 315, "tts": 371728}, {"pid": 660, "tid": 660, "ts": 39187653655, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 372058}, {"pid": 660, "tid": 660, "ts": 39187666801, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 372593}, {"pid": 660, "tid": 660, "ts": 39187667257, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 373048}, {"pid": 660, "tid": 660, "ts": 39187667274, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 204, "tdur": 204, "tts": 373064}, {"pid": 660, "tid": 660, "ts": 39187667492, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 61, "tts": 373282}, {"pid": 660, "tid": 660, "ts": 39187683579, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 373753}, {"pid": 660, "tid": 660, "ts": 39187683971, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 374144}, {"pid": 660, "tid": 660, "ts": 39187683987, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 206, "tdur": 207, "tts": 374159}, {"pid": 660, "tid": 660, "ts": 39187684208, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 63, "tts": 374380}, {"pid": 660, "tid": 660, "ts": 39187700125, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 374882}, {"pid": 660, "tid": 660, "ts": 39187700390, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 375146}, {"pid": 660, "tid": 660, "ts": 39187700404, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 183, "tdur": 183, "tts": 375159}, {"pid": 660, "tid": 660, "ts": 39187700600, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 89, "tdur": 88, "tts": 375356}, {"pid": 660, "tid": 660, "ts": 39187716340, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 375915}, {"pid": 660, "tid": 660, "ts": 39187716609, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 376184}, {"pid": 660, "tid": 660, "ts": 39187716624, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 341, "tdur": 342, "tts": 376198}, {"pid": 660, "tid": 660, "ts": 39187716982, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 376556}, {"pid": 660, "tid": 660, "ts": 39187733167, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 378782}, {"pid": 660, "tid": 660, "ts": 39187733438, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 379052}, {"pid": 660, "tid": 660, "ts": 39187733452, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 265, "tdur": 265, "tts": 379066}, {"pid": 660, "tid": 660, "ts": 39187733734, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 66, "tdur": 66, "tts": 379348}, {"pid": 660, "tid": 660, "ts": 39187749760, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 380000}, {"pid": 660, "tid": 660, "ts": 39187750058, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 380297}, {"pid": 660, "tid": 660, "ts": 39187750073, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 240, "tdur": 241, "tts": 380311}, {"pid": 660, "tid": 660, "ts": 39187750329, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 63, "tdur": 64, "tts": 380567}, {"pid": 660, "tid": 660, "ts": 39187767036, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 381125}, {"pid": 660, "tid": 660, "ts": 39187767476, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 381565}, {"pid": 660, "tid": 660, "ts": 39187767493, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 219, "tdur": 219, "tts": 381581}, {"pid": 660, "tid": 660, "ts": 39187767727, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 59, "tdur": 59, "tts": 381815}, {"pid": 660, "tid": 660, "ts": 39187783989, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 382265}, {"pid": 660, "tid": 660, "ts": 39187784505, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 382780}, {"pid": 660, "tid": 660, "ts": 39187784526, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 467, "tdur": 460, "tts": 382800}, {"pid": 660, "tid": 660, "ts": 39187785084, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 67, "tdur": 67, "tts": 383351}, {"pid": 660, "tid": 660, "ts": 39187800221, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 383990}, {"pid": 660, "tid": 660, "ts": 39187800546, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 384315}, {"pid": 660, "tid": 660, "ts": 39187800561, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 364, "tdur": 364, "tts": 384329}, {"pid": 660, "tid": 660, "ts": 39187800943, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 68, "tdur": 68, "tts": 384711}, {"pid": 660, "tid": 660, "ts": 39187817256, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 385240}, {"pid": 660, "tid": 660, "ts": 39187817518, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 385500}, {"pid": 660, "tid": 660, "ts": 39187817533, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 302, "tdur": 302, "tts": 385515}, {"pid": 660, "tid": 660, "ts": 39187817852, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 385834}, {"pid": 660, "tid": 660, "ts": 39187834070, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 386368}, {"pid": 660, "tid": 660, "ts": 39187834498, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 386796}, {"pid": 660, "tid": 660, "ts": 39187834514, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 825, "tdur": 826, "tts": 386811}, {"pid": 660, "tid": 660, "ts": 39187835356, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 60, "tts": 387654}, {"pid": 660, "tid": 660, "ts": 39187850596, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 388312}, {"pid": 660, "tid": 660, "ts": 39187850861, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 388576}, {"pid": 660, "tid": 660, "ts": 39187850876, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 211, "tdur": 211, "tts": 388590}, {"pid": 660, "tid": 660, "ts": 39187851101, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 61, "tdur": 60, "tts": 388816}, {"pid": 660, "tid": 660, "ts": 39187867008, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 389450}, {"pid": 660, "tid": 660, "ts": 39187867794, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 390236}, {"pid": 660, "tid": 660, "ts": 39187867815, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 257, "tdur": 257, "tts": 390256}, {"pid": 660, "tid": 660, "ts": 39187868089, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 88, "tdur": 88, "tts": 390530}, {"pid": 660, "tid": 660, "ts": 39187883266, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 391152}, {"pid": 660, "tid": 660, "ts": 39187883717, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 391603}, {"pid": 660, "tid": 660, "ts": 39187883742, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 326, "tdur": 291, "tts": 391627}, {"pid": 660, "tid": 660, "ts": 39187884096, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 89, "tdur": 89, "tts": 391946}, {"pid": 660, "tid": 660, "ts": 39187896172, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 392460}, {"pid": 660, "tid": 660, "ts": 39187896866, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 393153}, {"pid": 660, "tid": 660, "ts": 39187900286, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 362, "tdur": 331, "tts": 393377}, {"pid": 660, "tid": 660, "ts": 39187900665, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 80, "tdur": 86, "tts": 393725}, {"pid": 660, "tid": 660, "ts": 39187916442, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 394472}, {"pid": 660, "tid": 660, "ts": 39187922162, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 40}, "tts": 400174}, {"pid": 660, "tid": 660, "ts": 39187922180, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 55, "totalObjects": 55, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 400189}, {"pid": 660, "tid": 660, "ts": 39187922809, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 400820}, {"pid": 660, "tid": 660, "ts": 39187922827, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 269, "tdur": 269, "tts": 400837}, {"pid": 660, "tid": 660, "ts": 39187923116, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 128, "tdur": 128, "tts": 401125}, {"pid": 660, "tid": 660, "ts": 39187923290, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [797.0, 724.0, 1123.0, 724.0, 1123.0, 857.0, 797.0, 857.0], "nodeId": 8, "layerId": 14}}, "dur": 15, "tdur": 16, "tts": 401299}, {"pid": 660, "tid": 660, "ts": 39187923316, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [878.0, 727.0, 928.0, 715.0, 939.0, 766.0, 889.0, 777.0], "nodeId": 9, "layerId": 15}}, "dur": 565, "tdur": 566, "tts": 401325}, {"pid": 660, "tid": 660, "ts": 39187924040, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [582.0, 243.0, 879.0, 243.0, 879.0, 410.0, 582.0, 410.0], "nodeId": 12, "layerId": 18}}, "dur": 83, "tdur": 83, "tts": 402050}, {"pid": 660, "tid": 660, "ts": 39187955558, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 404282}, {"pid": 660, "tid": 660, "ts": 39187955884, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 404606}, {"pid": 660, "tid": 660, "ts": 39187955898, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 204, "tdur": 205, "tts": 404620}, {"pid": 660, "tid": 660, "ts": 39187956117, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 62, "tdur": 62, "tts": 404839}, {"pid": 660, "tid": 660, "ts": 39188517126, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 405498}, {"pid": 660, "tid": 660, "ts": 39188519902, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 40}, "tts": 408273}, {"pid": 660, "tid": 660, "ts": 39188519917, "ph": "B", "cat": "devtools.timeline", "name": "Layout", "args": {"beginData": {"dirtyObjects": 55, "totalObjects": 55, "partialLayout": false, "frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 408286}, {"pid": 660, "tid": 660, "ts": 39188520847, "ph": "E", "cat": "devtools.timeline", "name": "Layout", "args": {"endData": {"root": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "rootNode": 11}}, "tts": 409217}, {"pid": 660, "tid": 660, "ts": 39188520871, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 510, "tdur": 510, "tts": 409240}, {"pid": 660, "tid": 660, "ts": 39188521398, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 183, "tdur": 182, "tts": 409768}, {"pid": 660, "tid": 660, "ts": 39188521619, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [797.0, 724.0, 1123.0, 724.0, 1123.0, 857.0, 797.0, 857.0], "nodeId": 8, "layerId": 14}}, "dur": 20, "tdur": 20, "tts": 409988}, {"pid": 660, "tid": 660, "ts": 39188521655, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [882.0, 731.0, 865.0, 719.0, 877.0, 702.0, 894.0, 714.0], "nodeId": 9, "layerId": 15}}, "dur": 38, "tdur": 38, "tts": 410024}, {"pid": 660, "tid": 660, "ts": 39188521708, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [582.0, 243.0, 879.0, 243.0, 879.0, 410.0, 582.0, 410.0], "nodeId": 12, "layerId": 18}}, "dur": 58, "tdur": 59, "tts": 410077}, {"pid": 660, "tid": 660, "ts": 39188549589, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 410787}, {"pid": 660, "tid": 660, "ts": 39188549884, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 411082}, {"pid": 660, "tid": 660, "ts": 39188549898, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 212, "tdur": 213, "tts": 411095}, {"pid": 660, "tid": 660, "ts": 39188550127, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 80, "tdur": 80, "tts": 411325}, {"pid": 660, "tid": 660, "ts": 39188562547, "ph": "B", "cat": "blink_gc,devtools.timeline", "name": "BlinkGC.AtomicPhase", "args": {}, "tts": 414911}, {"pid": 660, "tid": 660, "ts": 39188565131, "ph": "E", "cat": "blink_gc,devtools.timeline", "name": "BlinkGC.AtomicPhase", "args": {}, "tts": 417440}, {"pid": 660, "tid": 660, "ts": 39188567047, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 417622}, {"pid": 660, "tid": 660, "ts": 39188567539, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 418114}, {"pid": 660, "tid": 660, "ts": 39188567558, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 292, "tdur": 261, "tts": 418132}, {"pid": 660, "tid": 660, "ts": 39188567870, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 81, "tdur": 81, "tts": 418413}, {"pid": 660, "tid": 660, "ts": 39188583445, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 419701}, {"pid": 660, "tid": 660, "ts": 39188583801, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 420056}, {"pid": 660, "tid": 660, "ts": 39188583816, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 282, "tdur": 250, "tts": 420071}, {"pid": 660, "tid": 660, "ts": 39188584116, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 82, "tdur": 82, "tts": 420339}, {"pid": 660, "tid": 660, "ts": 39188600124, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 420837}, {"pid": 660, "tid": 660, "ts": 39188600456, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 421169}, {"pid": 660, "tid": 660, "ts": 39188600472, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 311, "tdur": 269, "tts": 421184}, {"pid": 660, "tid": 660, "ts": 39188600800, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 83, "tdur": 83, "tts": 421470}, {"pid": 660, "tid": 660, "ts": 39188618495, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 422037}, {"pid": 660, "tid": 660, "ts": 39188618848, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 422389}, {"pid": 660, "tid": 660, "ts": 39188618863, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 237, "tdur": 237, "tts": 422404}, {"pid": 660, "tid": 660, "ts": 39188619124, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 90, "tdur": 89, "tts": 422666}, {"pid": 660, "tid": 660, "ts": 39188633544, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 423529}, {"pid": 660, "tid": 660, "ts": 39188633957, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 423942}, {"pid": 660, "tid": 660, "ts": 39188633980, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 312, "tdur": 283, "tts": 423963}, {"pid": 660, "tid": 660, "ts": 39188634316, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 108, "tdur": 108, "tts": 424270}, {"pid": 660, "tid": 660, "ts": 39188650024, "ph": "B", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"beginData": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "tts": 425062}, {"pid": 660, "tid": 660, "ts": 39188650353, "ph": "E", "cat": "blink,devtools.timeline", "name": "UpdateLayoutTree", "args": {"elementCount": 10}, "tts": 425391}, {"pid": 660, "tid": 660, "ts": 39188650369, "ph": "X", "cat": "devtools.timeline", "name": "UpdateLayerTree", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03"}}, "dur": 288, "tdur": 256, "tts": 425405}, {"pid": 660, "tid": 660, "ts": 39188650675, "ph": "X", "cat": "devtools.timeline,rail", "name": "Paint", "args": {"data": {"frame": "BFA6B40D1E4405E5925EEA32B23C3F03", "clip": [0.0, 0.0, 1920.0, 0.0, 1920.0, 1080.0, 0.0, 1080.0], "nodeId": 11, "layerId": 20}}, "dur": 81, "tdur": 81, "tts": 425678}, {"pid": 660, "tid": 0, "ts": 0, "ph": "M", "cat": "__metadata", "name": "num_cpus", "args": {"number": 2}}, {"pid": 660, "tid": 660, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_sort_index", "args": {"sort_index": -5}}, {"pid": 660, "tid": 660, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_name", "args": {"name": "<PERSON><PERSON><PERSON>"}}, {"pid": 660, "tid": 660, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_uptime_seconds", "args": {"uptime": 4}}, {"pid": 660, "tid": 660, "ts": 0, "ph": "M", "cat": "__metadata", "name": "process_labels", "args": {"labels": "BONUS on account:moneybag:generous promotions:gift:freespins in online casino"}}, {"pid": 660, "tid": 660, "ts": 0, "ph": "M", "cat": "__metadata", "name": "thread_sort_index", "args": {"sort_index": -1}}, {"pid": 660, "tid": 680, "ts": 0, "ph": "M", "cat": "__metadata", "name": "thread_name", "args": {"name": "ScriptStreamer thread"}}, {"pid": 660, "tid": 660, "ts": 0, "ph": "M", "cat": "__metadata", "name": "thread_name", "args": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}], "metadata": {"clock-domain": "LINUX_CLOCK_MONOTONIC", "command_line": "/root/.local/share/pyppeteer/local-chromium/588429/chrome-linux/chrome --disable-background-networking --disable-background-timer-throttling --disable-breakpad --disable-browser-side-navigation --disable-client-side-phishing-detection --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=site-per-process --disable-hang-monitor --disable-popup-blocking --disable-prompt-on-repost --disable-sync --disable-translate --metrics-recording-only --no-first-run --safebrowsing-disable-auto-update --enable-automation --password-store=basic --use-mock-keychain --headless --hide-scrollbars --mute-audio --no-sandbox --remote-debugging-port=37257 --user-data-dir=/root/.local/share/pyppeteer/.dev_profile/tmp732_xyq8 --use-gl=swiftshader-webgl --disable-gpu-compositing --file-url-path-alias=/gen=/root/.local/share/pyppeteer/local-chromium/588429/chrome-linux/gen about:blank", "cpu-brand": "Intel(R) Xeon(R) Platinum 8259CL CPU @ 2.50GHz", "cpu-family": 6, "cpu-model": 85, "cpu-stepping": 7, "gpu-devid": 65535, "gpu-driver": "4.1.0.2", "gpu-gl-renderer": "Google SwiftShader", "gpu-gl-vendor": "Google Inc.", "gpu-psver": "3.00", "gpu-venid": 65535, "gpu-vsver": "3.00", "highres-ticks": true, "network-type": "Unknown", "num-cpus": 2, "os-arch": "x86_64", "os-name": "Linux", "os-version": "5.4.0-1039-aws", "physical-memory": 3876, "product-version": "HeadlessChrome/71.0.3542.0", "trace-capture-datetime": "2024-4-1 17:57:52", "trace-config": "{\"enable_argument_filter\":false,\"enable_systrace\":false,\"included_categories\":[\"devtools.timeline\"],\"included_process_ids\":[635,660],\"record_mode\":\"record-until-full\"}", "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/71.0.3542.0 Safari/537.36", "v8-version": "7.0.276.3"}}