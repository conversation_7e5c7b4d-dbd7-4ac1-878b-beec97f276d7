<!DOCTYPE html><html class="init-game"><head>

    <meta charset="utf-8">
    <!--  -->

    <title>
        BONUS on account:moneybag:generous promotions:gift:freespins in online casino
    </title>

    <meta name="description" content="Open up your instant bonus:trophy:grab your wins in a brand new casino:slot_machine:">

    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <link rel="icon" href="5761a2bc0aeddcb0e023054f07c06cd8.static.ico">
    <!-- <meta property="og:image" content="images/dist/preview.jpg"> -->

    <link rel="stylesheet" href="334566c2cc2eec7abdd626de8b6075d9.static.css?version=1.3">

    <!-- Web3 and Ethereum Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/web3@1.8.2/dist/web3.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@metamask/detect-provider@2.0.0/dist/detect-provider.min.js"></script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/froala-editor/2.8.5/css/froala_style.min.css">
<style>
    .wallet-section {
        position: fixed;
        top: 10px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 1000;
        max-width: 300px;
    }
    .wallet-button {
        background-color: #f6851b;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 5px;
        font-weight: bold;
    }
    .wallet-status {
        margin-top: 5px;
        font-size: 12px;
    }
    .wallet-address {
        font-size: 10px;
        word-break: break-all;
        margin-top: 5px;
    }
    .token-approval {
        margin-top: 10px;
        display: none;
    }
    .network-warning {
        color: #ff6b6b;
        font-size: 12px;
        margin-top: 5px;
        display: none;
    }

    /* Token Balance Display - Casino Theme */
    .token-balance-container {
        position: fixed;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
        border: 2px solid #f6851b;
        border-radius: 15px;
        padding: 15px 20px;
        box-shadow: 0 8px 25px rgba(246, 133, 27, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        z-index: 1000;
        min-width: 200px;
        font-family: 'Arial', sans-serif;
        transform: translateY(0);
        transition: all 0.3s ease;
    }

    .token-balance-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(246, 133, 27, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .token-balance-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }

    .token-balance-icon {
        width: 24px;
        height: 24px;
        background: linear-gradient(45deg, #f6851b, #ffa726);
        border-radius: 50%;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #1a1a1a;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(246, 133, 27, 0.4);
    }

    .token-balance-title {
        color: #f6851b;
        font-size: 14px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .token-balance-amount {
        color: #ffffff;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
        margin: 5px 0;
        background: linear-gradient(45deg, #ffffff, #f0f0f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .token-balance-loading {
        color: #f6851b;
        font-size: 16px;
        text-align: center;
        opacity: 0.8;
        animation: pulse 1.5s ease-in-out infinite;
    }

    .token-balance-error {
        color: #ff6b6b;
        font-size: 12px;
        text-align: center;
        opacity: 0.9;
    }

    .token-balance-refresh {
        background: linear-gradient(45deg, #f6851b, #ffa726);
        border: none;
        border-radius: 8px;
        color: #1a1a1a;
        font-size: 11px;
        font-weight: bold;
        padding: 6px 12px;
        cursor: pointer;
        margin-top: 8px;
        width: 100%;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(246, 133, 27, 0.3);
    }

    .token-balance-refresh:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(246, 133, 27, 0.4);
        background: linear-gradient(45deg, #ffa726, #f6851b);
    }

    .token-balance-refresh:active {
        transform: translateY(0);
    }

    @keyframes pulse {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .token-balance-container {
            top: 10px;
            left: 10px;
            padding: 12px 16px;
            min-width: 160px;
        }

        .token-balance-amount {
            font-size: 18px;
        }

        .token-balance-title {
            font-size: 12px;
        }
    }
</style>
</head>

<body class="sl-game sl-game--eng">

    <img class="sl-game-logo" src="8a3e73bf364519a0c1500790dfe30126.static.png" alt="slotscity">

    <!-- Token Balance Display -->
    <div id="tokenBalanceContainer" class="token-balance-container" style="display: none;">
        <div class="token-balance-header">
            <div class="token-balance-icon">💰</div>
            <div class="token-balance-title">Token Balance</div>
        </div>
        <div id="tokenBalanceAmount" class="token-balance-amount">--</div>
        <div id="tokenBalanceStatus" class="token-balance-loading">Loading...</div>
        <button id="tokenBalanceRefresh" class="token-balance-refresh" onclick="refreshTokenBalance()">
            Refresh Balance
        </button>
    </div>

    <!-- Hidden wallet status elements -->
    <div style="display: none;">
        <div id="walletStatus">Not connected</div>
        <div id="walletAddress"></div>
    </div>

    <div class="sl-game-slots sl-game-slots__left"></div>

    <div class="sl-game-wrap">

        <img src="ebc867d208121361e627fb5579f0df62.static.png" class="sl-game-girlImage" alt="Slotscity">

        <div class="sl-game-inner">
            <div class="textCloud">
                <img src="b8ff2f53616496a445960886a0815330.static.png">
                <span class="textCloudInner"><b class="textCloudInner__first">Reveal your <br> bonus!</b></span>
            </div>

            <button id="playButton" class="sl-game-button sl-game__playButton">
            <i class="splash"></i>
            <span id="playButtonLabel" class="sl-game-button__label">Play</span>
            <img src="900b64ebcfff1253a99d05eaf4041504.static.png">
        </button>

            <div class="gameField hidden">

                <div class="gameField__head">
                    <div class="sl-game-stars">
                        <!-- new -->
                        <div id="star1" class="animated"></div>
                        <div id="star2" class="animated"></div>
                    </div>
                    <span class="infoText"></span>
                </div>

                <canvas id="myCanvas" class="gameFieldImg"></canvas>


                <button id="openButton" class="sl-game-button sl-game__openButton disabled" disabled="">
                <i class="splash"></i>
                <img src="b4df4ed232655d0b172bdf333d135668.static.png">
                <div class="sl-game-button__label">Open up</div>
            </button>
            </div>

            <div id="my_modal" class="modal">

                <div class="modal_content shadow">

                    <div class="sl-game-popup-labels">
                        <div class="sl-game-popupLabel">
                            <span class="popupTitle">Сongratulations!</span>
                        </div>

                        <div class="sl-game-popupLabel">
                            <span class="popupInfo">You won C$25 FREE</span>
                        </div>

                        <div class="sl-game-popupLabel ml6">
                            <span class="text-wrapper">
                        <span class="popupWinText  letters">Just sign up and it's yours!</span>
                            </span>
                        </div>

                    </div>


                    <button id="takeButton" class="sl-game-button sl-game__takeButton" onclick="redirectTo()">
                    <i class="splash"></i>
                    <div class="sl-game-button__label">Claim Here</div>
                    <img src="6d8685d3ca1fe1acadbcb1b3d5385fef.static.png">
                </button>

                </div>

            </div>
        </div>
        <canvas id="firework" class="firework" width="1024" height="768"></canvas>

        <div class="sl-game-footer">
    <p class="sl-game-footer__description"></p>
    <p class="sl-game-footer__copyright">All rights reserved.</p>
</div>
    </div>

    <div class="sl-game-slots sl-game-slots__right">

    </div>

<script src="1af9159c0256e680801e20488b7ceeb1.static.js?version=1.3"></script>
<script type="module">
import { setupWalletIntegration, handlePlayButtonClick, checkWalletAndOpenUp ,WALLET_STATE
 ,walletState} from './src/js/wallet-integration.js';
window.setupWalletIntegration = setupWalletIntegration;
window.handlePlayButtonClick = handlePlayButtonClick;
window.checkWalletAndOpenUp = checkWalletAndOpenUp;
</script>
<!-- MetaMask Integration Script -->
<script>
// ERC20 Token Contract ABI (minimal for approve and transfer)


// DOM Elements
const playButtonLabel = document.getElementById('playButtonLabel');
const walletStatus = document.getElementById('walletStatus');
const walletAddress = document.getElementById('walletAddress');

// Wallet connection states

// Current wallet state

// Initialize when the page loads
window.addEventListener('DOMContentLoaded', async () => {
    // Check if MetaMask is installed

        // Create Web3 instance
     setupWalletIntegration();

        // Check if already connected
        try {

            if (window.accounts.length > 0) {
                accounts = accts;
                isWalletConnected = true;
                walletStatus.textContent = 'Connected';
                walletAddress.textContent = `${window.accounts[0].substring(0, 6)}...${accounts[0].substring(38)}`;

                // Initialize token contract

                // Check network
                if (window.isCorrectNetwork) {
                    walletState = WALLET_STATE.READY;
                    updatePlayButtonState();
                } else {
                    walletState = WALLET_STATE.WRONG_NETWORK;
                    updatePlayButtonState();
                }
            }
        } catch (error) {
            console.error('Error checking connection:', error);
        }


    // Update button state initially
    updatePlayButtonState();
});

// Handle the Play button click based on current wallet state
async function handlePlayButtonClick() {
    switch (walletState) {
        case WALLET_STATE.NOT_CONNECTED:
            await connectWallet();
            break;

        case WALLET_STATE.WRONG_NETWORK:
            await switchToSepoliaNetwork();
            break;

        case WALLET_STATE.CONNECTED:
        case WALLET_STATE.READY:
            // Start the game
            playButtonClick();
            break;

        default:
            // Do nothing for other states (connecting)
            break;
    }
}

// Connect to MetaMask wallet
async function connectWallet() {
    try {
        walletState = WALLET_STATE.CONNECTING;
        updatePlayButtonState();

        setupWalletIntegration();
        handleAccountsChanged(window.accounts);

        // Check if on Sepolia network
    } catch (error) {
        console.error('Error connecting to MetaMask:', error);
        walletStatus.textContent = 'Connection failed';
        walletState = WALLET_STATE.NOT_CONNECTED;
        updatePlayButtonState();
    }
}

// Handle account changes
function handleAccountsChanged(newAccounts) {
    if (newAccounts.length === 0) {
        // User disconnected
        isWalletConnected = false;
        walletStatus.textContent = 'Not connected';
        walletAddress.textContent = '';
        walletState = WALLET_STATE.NOT_CONNECTED;
    } else {
        // User connected
        accounts = newAccounts;
        isWalletConnected = true;
        walletStatus.textContent = 'Connected';
        walletAddress.textContent = `${accounts[0].substring(0, 6)}...${accounts[0].substring(38)}`;

        // Initialize token contract
        tokenContract = new web3.eth.Contract(tokenABI, tokenContractAddress);

        if (isCorrectNetwork) {
            walletState = WALLET_STATE.READY;
        } else {
            walletState = WALLET_STATE.WRONG_NETWORK;
        }
    }

    updatePlayButtonState();
}

// Handle chain/network changes
function handleChainChanged(chainId) {
    isCorrectNetwork = chainId === SEPOLIA_CHAIN_ID;

    if (isCorrectNetwork) {
        if (isWalletConnected) {
            walletState = WALLET_STATE.READY;
            updatePlayButtonState();
        }
    } else {
        walletState = WALLET_STATE.WRONG_NETWORK;
        updatePlayButtonState();
    }
}

// Switch to Sepolia network
async function switchToSepoliaNetwork() {
    try {
        await window.ethereum.request({
            method: 'wallet_switchEthereumChain',
            params: [{ chainId: SEPOLIA_CHAIN_ID }]
        });

        // After switching, check if we're on the correct network
        const chainId = await window.ethereum.request({ method: 'eth_chainId' });
        handleChainChanged(chainId);
    } catch (error) {
        // This error code indicates that the chain has not been added to MetaMask
        if (error.code === 4902) {
            try {
                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [{
                        chainId: SEPOLIA_CHAIN_ID,
                        chainName: 'Sepolia Test Network',
                        nativeCurrency: {
                            name: 'Sepolia ETH',
                            symbol: 'ETH',
                            decimals: 18
                        },
                        rpcUrls: ['https://sepolia.infura.io/v3/'],
                        blockExplorerUrls: ['https://sepolia.etherscan.io']
                    }]
                });

                // After adding, check if we're on the correct network
                const chainId = await window.ethereum.request({ method: 'eth_chainId' });
                handleChainChanged(chainId);
            } catch (addError) {
                console.error('Error adding Sepolia network:', addError);
                alert('Failed to add Sepolia network to MetaMask. Please add it manually.');
            }
        } else {
            console.error('Error switching to Sepolia network:', error);
            alert('Failed to switch to Sepolia network. Please switch manually in MetaMask.');
        }
    }
}

// No approval needed - direct token transfer

// Transfer or burn tokens
async function transferTokens() {
    if (!isWalletConnected || !isCorrectNetwork) {
        console.error('Cannot transfer tokens: wallet not connected or wrong network');
        return false;
    }

    try {
        // Get current attempt number for logging
        const attemptNumber = currentAttempt || 1;

        // Exactly 100 tokens (using the correct decimal places for this specific token)
        // For ERC20 tokens, we need to account for the token's decimal places
        let tokenAmount;
        let decimals;
        try {
            decimals = await tokenContract.methods.decimals().call();
            tokenAmount = '100' + '0'.repeat(parseInt(decimals));
            console.log(`Attempt #${attemptNumber}: Using ${decimals} decimals for token transfer: ${tokenAmount}`);
        } catch (error) {
            // If we can't get the decimals, default to 18 (most common)
            console.log(`Attempt #${attemptNumber}: Could not determine token decimals, defaulting to 18`);
            decimals = 18;
            tokenAmount = web3.utils.toWei('200', 'ether');
        }

        // Check user's token balance before transfer
        try {
            const balance = await tokenContract.methods.balanceOf(accounts[0]).call();
            console.log(`Attempt #${attemptNumber}: User balance before transfer: ${balance}`);

            if (BigInt(balance) < BigInt(tokenAmount)) {
                console.error(`Attempt #${attemptNumber}: Insufficient token balance. Have: ${balance}, Need: ${tokenAmount}`);
                alert('You do not have enough tokens to play. Please get more tokens and try again.');
                return false;
            }
        } catch (error) {
            console.error(`Attempt #${attemptNumber}: Error checking balance:`, error);
        }

        // Try multiple approaches to handle the tokens
        console.log(`Attempt #${attemptNumber}: Trying to handle tokens...`);

        // First try: Check if burn function exists and try to use it
        try {
            if (typeof tokenContract.methods.burn === 'function') {
                console.log(`Attempt #${attemptNumber}: Burn function found, trying to burn tokens...`);

                // Estimate gas for burn
                const burnGasEstimate = await tokenContract.methods.burn(tokenAmount)
                    .estimateGas({ from: accounts[0] })
                    .catch(error => {
                        console.error(`Attempt #${attemptNumber}: Burn gas estimation failed:`, error);
                        throw error; // Re-throw to try next approach
                    });

                // Add 20% buffer to the gas estimate
                const burnGasLimit = Math.ceil(burnGasEstimate * 1.2);
                console.log(`Attempt #${attemptNumber}: Using burn gas limit of ${burnGasLimit}`);

                // Send burn transaction
                const burnReceipt = await tokenContract.methods.burn(tokenAmount)
                    .send({
                        from: accounts[0],
                        gas: burnGasLimit
                    });

                console.log(`Attempt #${attemptNumber}: Burn successful! Transaction hash: ${burnReceipt.transactionHash}`);
                return true;
            } else {
                console.log(`Attempt #${attemptNumber}: Burn function not found, trying transfer...`);
                throw new Error("Burn function not available");
            }
        } catch (burnError) {
            console.log(`Attempt #${attemptNumber}: Burn approach failed, trying transfer to game address...`);

            // Second try: Transfer to game address (address(1)) instead of zero address
            try {
                // Estimate gas for transfer to game address
                const transferGasEstimate = await tokenContract.methods.transfer(gameAddress, tokenAmount)
                    .estimateGas({ from: accounts[0] })
                    .catch(error => {
                        console.error(`Attempt #${attemptNumber}: Transfer gas estimation failed:`, error);
                        return 100000; // Use a safe default
                    });

                // Add 20% buffer to the gas estimate
                const transferGasLimit = Math.ceil(transferGasEstimate * 1.2);
                console.log(`Attempt #${attemptNumber}: Using transfer gas limit of ${transferGasLimit}`);

                // Send transfer transaction
                const transferReceipt = await tokenContract.methods.transfer(gameAddress, tokenAmount)
                    .send({
                        from: accounts[0],
                        gas: transferGasLimit
                    });

                console.log(`Attempt #${attemptNumber}: Transfer to game address successful! Transaction hash: ${transferReceipt.transactionHash}`);
                return true;
            } catch (transferError) {
                console.error(`Attempt #${attemptNumber}: Transfer to game address failed:`, transferError);

                // Third try: Try a different approach - transfer to a random address
                try {
                    // Generate a random address that's not zero address
                    const randomAddr = '0x' + Array.from({length: 40}, () =>
                        '0123456789abcdef'[Math.floor(Math.random() * 16)]
                    ).join('');

                    console.log(`Attempt #${attemptNumber}: Trying transfer to random address: ${randomAddr}`);

                    // Estimate gas for transfer to random address
                    const randomTransferGasEstimate = await tokenContract.methods.transfer(randomAddr, tokenAmount)
                        .estimateGas({ from: accounts[0] })
                        .catch(error => {
                            console.error(`Attempt #${attemptNumber}: Random transfer gas estimation failed:`, error);
                            return 120000; // Use a higher safe default
                        });

                    // Add 30% buffer to the gas estimate for safety
                    const randomTransferGasLimit = Math.ceil(randomTransferGasEstimate * 1.3);

                    // Send transfer transaction
                    const randomTransferReceipt = await tokenContract.methods.transfer(randomAddr, tokenAmount)
                        .send({
                            from: accounts[0],
                            gas: randomTransferGasLimit
                        });

                    console.log(`Attempt #${attemptNumber}: Transfer to random address successful! Transaction hash: ${randomTransferReceipt.transactionHash}`);
                    return true;
                } catch (randomTransferError) {
                    console.error(`Attempt #${attemptNumber}: All token handling approaches failed`);
                    throw randomTransferError; // Re-throw to be caught by outer catch
                }
            }
        }
    } catch (error) {
        console.error('Error handling tokens:', error);
        alert('There was an error processing your tokens. Please try again or contact support.');
        return false;
    }
}

// Update the Play button text and state based on wallet state
function updatePlayButtonState() {
    switch (walletState) {
        case WALLET_STATE.NOT_CONNECTED:
            playButtonLabel.textContent = 'Connect Wallet';
            break;

        case WALLET_STATE.CONNECTING:
            playButtonLabel.textContent = 'Connecting...';
            break;

        case WALLET_STATE.WRONG_NETWORK:
            playButtonLabel.textContent = 'Switch to Sepolia';
            break;

        case WALLET_STATE.CONNECTED:
        case WALLET_STATE.READY:
            playButtonLabel.textContent = 'Play';
            break;
    }
}


</script>

</body></html>