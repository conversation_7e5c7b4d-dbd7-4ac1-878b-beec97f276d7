{"name": "jackpot-game", "version": "1.0.0", "description": "Jackpot game with Reown AppKit wallet integration", "type": "module", "scripts": {"start": "node server.js", "dev": "vite", "build": "vite build", "preview": "vite preview", "pm2": "pm2 start ecosystem.config.js --env production", "pm2:restart": "pm2 restart jackpot-game", "pm2:logs": "pm2 logs jackpot-game"}, "dependencies": {"@gelatonetwork/relay-sdk": "^5.6.0", "@reown/appkit": "1.7.4", "@reown/appkit-adapter-ethers": "1.7.4", "compression": "^1.7.4", "cors": "^2.8.5", "ethers": "^6.14.1", "express": "^4.18.2", "helmet": "^7.0.0", "morgan": "^1.10.0", "viem": "^2.30.1", "web3": "1.10.0"}, "devDependencies": {"nodemon": "^2.0.22", "vite": "5.0.8", "vite-plugin-static-copy": "^1.0.6"}, "engines": {"node": ">=16.0.0"}}